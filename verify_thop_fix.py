#!/usr/bin/env python3
"""
验证thop库冲突修复是否成功
"""

import os
import re

def check_thop_fix(file_path):
    """检查单个文件是否已修复thop问题"""
    
    if not os.path.exists(file_path):
        return False, "文件不存在"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含清理函数
        has_clean_function = "def clean_thop_attributes(module):" in content
        has_clean_call = "clean_thop_attributes(model)" in content
        has_profile = "macs, params = profile(model" in content
        
        if has_profile and has_clean_function and has_clean_call:
            return True, "已修复"
        elif has_profile and not (has_clean_function and has_clean_call):
            return False, "需要修复"
        else:
            return True, "无需修复"
            
    except Exception as e:
        return False, f"检查失败: {e}"

def main():
    """主函数"""
    print("🔍 验证thop库冲突修复状态...")
    
    # 需要检查的DQN变体
    dqn_variants = [
        "DQN",
        "DoubleDQN",
        "DuelingDQN", 
        "EWC_DQN",
        "LSTM_DQN",
        "PER_DQN",
        "RDQN",
        "Attention_DQN",
        "RDQN_Ablation"
    ]
    
    results = {}
    
    for variant in dqn_variants:
        train_file = os.path.join(variant, "train.py")
        is_fixed, status = check_thop_fix(train_file)
        results[variant] = (is_fixed, status)
        
        status_icon = "✅" if is_fixed else "❌"
        print(f"  {status_icon} {variant:<20}: {status}")
    
    # 统计结果
    fixed_count = sum(1 for is_fixed, _ in results.values() if is_fixed)
    total_count = len(results)
    
    print(f"\n{'='*60}")
    print(f"📊 修复状态统计")
    print(f"{'='*60}")
    print(f"✅ 已修复: {fixed_count}/{total_count} ({fixed_count/total_count*100:.1f}%)")
    
    if fixed_count == total_count:
        print(f"\n🎉 所有DQN变体的thop冲突问题都已修复!")
        print(f"🚀 现在可以正常训练了:")
        print(f"   cd DQN && python train.py --epochs 50")
        print(f"   cd DoubleDQN && python train.py --epochs 50")
        print(f"   # ... 等等")
        
        print(f"\n📋 或者使用验证脚本:")
        print(f"   python verify_integration.py")
        
    else:
        print(f"\n⚠️ 还有 {total_count - fixed_count} 个变体需要修复")
        for variant, (is_fixed, status) in results.items():
            if not is_fixed:
                print(f"   ❌ {variant}: {status}")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
