#!/usr/bin/env python3
"""
PER-DQN 实现测试脚本
验证优先经验回放DQN的核心功能是否正确实现
"""

import torch
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import PER_DQN, PrioritizedReplayBuffer

def test_prioritized_replay_buffer():
    """测试优先经验回放缓冲区"""
    print("=== 测试优先经验回放缓冲区 ===")
    
    # 创建缓冲区
    buffer = PrioritizedReplayBuffer(capacity=1000, alpha=0.6, beta=0.4)
    
    # 添加一些经验
    for i in range(10):
        state = np.random.randn(100)
        action = np.random.randint(0, 9)
        reward = np.random.randn()
        next_state = np.random.randn(100)
        td_error = np.random.randn()
        
        buffer.add(state, action, reward, next_state, td_error)
    
    print(f"缓冲区大小: {len(buffer)}")
    
    # 测试采样
    if len(buffer) >= 5:
        batch, indices, weights = buffer.sample(5)
        print(f"采样批次大小: {len(batch)}")
        print(f"重要性采样权重: {weights}")
        
        # 测试优先级更新
        td_errors = np.random.randn(5)
        buffer.update_priorities(indices, td_errors)
        print("优先级更新成功")
    
    print("优先经验回放缓冲区测试通过 ✓\n")

def test_per_dqn():
    """测试PER-DQN主类"""
    print("=== 测试PER-DQN主类 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建PER-DQN
    per_dqn = PER_DQN(
        device=device,
        state_dim=100,
        memory_capacity=1000,
        batch_size=32,
        lr=1e-4,
        gamma=0.9,
        q_network_iteration=10
    )
    
    print(f"PER-DQN创建成功")
    print(f"状态维度: {per_dqn.state_dim}")
    print(f"记忆容量: {per_dqn.memory_capacity}")
    print(f"批次大小: {per_dqn.batch_size}")
    
    # 测试动作选择
    state = np.random.randn(100)
    action = per_dqn.choose_action(state, EPISILO=0.1)
    print(f"选择的动作: {action}")
    
    # 测试经验存储
    next_state = np.random.randn(100)
    reward = np.random.randn()
    per_dqn.store_transition(state, action, reward, next_state)
    print(f"经验存储成功，当前记忆计数: {per_dqn.memory_counter}")
    
    # 添加更多经验以测试学习
    for i in range(50):
        state = np.random.randn(100)
        action = np.random.randint(0, 9)
        reward = np.random.randn()
        next_state = np.random.randn(100)
        per_dqn.store_transition(state, action, reward, next_state)
    
    print(f"总经验数: {per_dqn.memory_counter}")
    
    # 测试学习
    if per_dqn.memory_counter >= per_dqn.batch_size:
        initial_loss_len = len(per_dqn.train_loss)
        per_dqn.learn()
        print(f"学习步骤执行成功")
        print(f"学习步数计数器: {per_dqn.learn_step_counter}")
        if len(per_dqn.train_loss) > initial_loss_len:
            print(f"最新损失: {per_dqn.train_loss[-1]:.4f}")
    
    print("PER-DQN主类测试通过 ✓\n")

def test_target_network_update():
    """测试目标网络更新机制"""
    print("=== 测试目标网络更新机制 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建PER-DQN，设置较小的更新间隔
    per_dqn = PER_DQN(
        device=device,
        state_dim=100,
        memory_capacity=1000,
        batch_size=32,
        lr=1e-4,
        gamma=0.9,
        q_network_iteration=5  # 每5步更新一次
    )
    
    # 添加足够的经验
    for i in range(50):
        state = np.random.randn(100)
        action = np.random.randint(0, 9)
        reward = np.random.randn()
        next_state = np.random.randn(100)
        per_dqn.store_transition(state, action, reward, next_state)
    
    # 记录初始目标网络参数
    initial_target_params = {}
    for name, param in per_dqn.target_net.named_parameters():
        initial_target_params[name] = param.clone()
    
    # 执行多次学习，触发目标网络更新
    for i in range(10):
        per_dqn.learn()
    
    print(f"执行了 {per_dqn.learn_step_counter} 次学习步骤")
    print(f"目标网络更新记录: {per_dqn.learn_step}")
    
    # 检查目标网络是否更新
    target_updated = False
    for name, param in per_dqn.target_net.named_parameters():
        if not torch.equal(initial_target_params[name], param):
            target_updated = True
            break
    
    if target_updated:
        print("目标网络更新机制工作正常 ✓")
    else:
        print("警告: 目标网络可能未正确更新")
    
    print("目标网络更新测试完成\n")

def main():
    """主测试函数"""
    print("开始PER-DQN实现测试...\n")
    
    try:
        # 测试各个组件
        test_prioritized_replay_buffer()
        test_per_dqn()
        test_target_network_update()
        
        print("=== 所有测试通过 ===")
        print("PER-DQN实现正确！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
