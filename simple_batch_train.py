#!/usr/bin/env python3
"""
简化的批量训练脚本 - 避免subprocess阻塞问题
直接调用训练函数而不是启动子进程
"""

import os
import sys
import time
from datetime import datetime
import importlib

def train_single_method(method_name, epochs=50):
    """训练单个DQN方法"""
    print(f"\n{'='*80}")
    print(f"[开始] 训练 {method_name}")
    print(f"训练轮次: {epochs}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*80}")

    try:
        # 处理RDQN消融实验变体
        if method_name.startswith('RDQN_') and method_name != 'RDQN_Ablation':
            method_dir = 'RDQN_Ablation'  # 使用RDQN_Ablation目录
            model_class = method_name     # 使用对应的模型类名
            save_dir_suffix = method_name  # 保存目录使用变体名称
        else:
            method_dir = method_name
            model_class = None  # 使用默认的DQN类
            save_dir_suffix = method_name

        if not os.path.exists(method_dir):
            print(f"[错误] 目录不存在: {method_dir}")
            return False
        
        # 保存当前目录
        original_dir = os.getcwd()
        
        try:
            # 切换到方法目录
            os.chdir(method_dir)
            
            # 使用绝对路径导入train模块
            train_file_path = os.path.join(os.getcwd(), 'train.py')
            print(f"[调试] 尝试加载train文件: {train_file_path}")

            if not os.path.exists(train_file_path):
                raise FileNotFoundError(f"train.py文件不存在: {train_file_path}")

            # 彻底清理之前的模块缓存
            modules_to_clean = ['train', 'models', 'data', 'hilbert_mapping', 'utils', 'net']
            for module_name in list(sys.modules.keys()):
                if any(module_name.endswith(clean_name) for clean_name in modules_to_clean):
                    del sys.modules[module_name]

            # 使用importlib.util动态加载模块，确保使用当前目录的文件
            spec = importlib.util.spec_from_file_location("train", train_file_path)
            train = importlib.util.module_from_spec(spec)
            sys.modules["train"] = train
            spec.loader.exec_module(train)
            
            # 创建模拟的命令行参数
            class Args:
                def __init__(self):
                    # 基本参数
                    self.save_dir = f'aeroplane_{save_dir_suffix}'  # 为每个方法创建独立的保存目录
                    self.eval_set = 'aeroplane_test'
                    self.use_gpu = True
                    self.gpu_devices = '0'
                    self.test_voc_path = None

                    # 模型加载参数
                    self.load_dqn = None
                    self.load_refine = None
                    self.load_model = None  # 添加缺少的load_model参数

                    # 训练阶段控制
                    self.skip_rl = False
                    self.skip_refine = True
                    self.skip_joint = True
                    self.eval_only = False

                    # 训练轮次参数
                    self.rl_epochs = epochs
                    self.refine_epochs = 0
                    self.joint_epochs = 0

                    # FLOPs计算参数
                    self.compute_flops = False  # 跳过FLOPs计算以加快速度
                    self.flops_detail = False
                    self.flops_backbone = False

                    # 其他参数
                    self.EPISILO = 0.9  # e-贪婪系数

                    # RDQN消融实验特殊参数
                    self.model_class = model_class  # 指定使用的模型类
            
            args = Args()
            
            # 调用训练主函数
            print(f"[执行] 开始训练 {method_name}...")
            print(f"[调试] 当前工作目录: {os.getcwd()}")
            print(f"[调试] train模块路径: {train.__file__}")
            train.main(args)
            
            print(f"[成功] {method_name} 训练完成")
            return True
            
        finally:
            # 恢复原始目录
            os.chdir(original_dir)

            # 彻底清理导入的模块，避免冲突
            modules_to_remove = []
            for module_name in list(sys.modules.keys()):
                # 清理所有相关模块，包括带路径的模块
                if any(clean_name in module_name for clean_name in ['train', 'models', 'data', 'hilbert_mapping', 'utils', 'net']):
                    modules_to_remove.append(module_name)

            for module_name in modules_to_remove:
                if module_name in sys.modules:
                    del sys.modules[module_name]

            print(f"[调试] 清理了 {len(modules_to_remove)} 个模块: {modules_to_remove}")

            # 清理Python路径
            while '.' in sys.path:
                sys.path.remove('.')
    except Exception as e:
        print(f"[失败] {method_name} 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def train_all_methods(epochs=50):
    """批量训练所有DQN方法"""
    print("[开始] 批量训练所有DQN方法")
    print(f"每个方法训练轮次: {epochs}")
    print("="*80)
    
    # 需要训练的方法列表
    methods = [
        'DQN',
        'DoubleDQN',
        'DuelingDQN',
        'PER_DQN',
        'LSTM_DQN',
        'EWC_DQN',
        'Attention_DQN',
        'RDQN',  # 完整RDQN方法
        'RDQN_Ablation',  # RDQN消融实验基础版本（使用DQN类）
        # RDQN消融实验变体（在RDQN_Ablation目录中，使用不同的模型类）
        'RDQN_NoImportance',     # 消融实验1：移除重要性调制
        'RDQN_NoMetaplasticity', # 消融实验2：移除元可塑性机制
        'RDQN_UniformSync',      # 消融实验3：统一同步策略
    ]
    
    successful_methods = []
    failed_methods = []
    
    start_time = time.time()
    
    for i, method in enumerate(methods, 1):
        print(f"\n[{i}/{len(methods)}] 训练 {method}...")
        
        method_start_time = time.time()
        success = train_single_method(method, epochs)
        method_end_time = time.time()
        
        method_duration = method_end_time - method_start_time
        print(f"[时间] {method} 耗时: {method_duration:.1f}秒")
        
        if success:
            successful_methods.append(method)
        else:
            failed_methods.append(method)
    
    total_time = time.time() - start_time
    
    # 输出最终结果
    print("\n" + "="*80)
    print("[结果] 批量训练结果汇总")
    print("="*80)
    print(f"[成功] 成功: {len(successful_methods)}/{len(methods)} 个方法")
    print(f"[失败] 失败: {len(failed_methods)} 个方法")
    print(f"[时间] 总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    
    if successful_methods:
        print(f"\n[成功方法]:")
        for method in successful_methods:
            print(f"   - {method}")
    
    if failed_methods:
        print(f"\n[失败方法]:")
        for method in failed_methods:
            print(f"   - {method}")
    
    print("="*80)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简化批量训练脚本')
    parser.add_argument('--method', type=str, help='训练单个方法')
    parser.add_argument('--epochs', type=int, default=50, help='训练轮次')
    parser.add_argument('--all', action='store_true', help='训练所有方法')
    
    args = parser.parse_args()
    
    if args.all:
        train_all_methods(args.epochs)
    elif args.method:
        train_single_method(args.method, args.epochs)
    else:
        print("请指定 --method <方法名> 或 --all")
        print("可用方法:")
        print("  基础DQN变体: DQN, DoubleDQN, DuelingDQN, PER_DQN, LSTM_DQN, EWC_DQN, Attention_DQN")
        print("  RDQN方法: RDQN, RDQN_Ablation")
        print("  RDQN消融实验: RDQN_NoImportance, RDQN_NoMetaplasticity, RDQN_UniformSync")

if __name__ == "__main__":
    main()
