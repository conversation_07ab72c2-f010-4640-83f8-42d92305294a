#!/usr/bin/env python3
"""
测试DQN变体集成的数据记录功能
"""

import os
import sys
from dqn_metrics_recorder import DQNMetricsRecorder
from dqn_analysis_plotter import DQNAnalysisPlotter

def test_metrics_recorder():
    """测试指标记录器功能"""
    print("🧪 测试DQN指标记录器...")
    
    # 创建测试记录器
    recorder = DQNMetricsRecorder("TestDQN", "test_dqn_metrics")
    
    # 模拟训练过程
    import numpy as np
    for epoch in range(1, 11):
        # 模拟训练数据
        reward = -2000 + epoch * 150 + np.random.normal(0, 50)
        actions = np.random.choice(9, size=100)  # 模拟100个动作
        
        # 记录epoch数据
        recorder.record_epoch(epoch, reward, actions)
        
        # 每5个epoch进行评估
        if epoch % 5 == 0:
            ap50 = 0.3 + epoch * 0.05 + np.random.normal(0, 0.02)
            map_value = ap50 * 0.8
            recorder.record_evaluation(epoch, ap50, map_value)
    
    # 完成训练
    recorder.finalize_training()
    
    print("✅ 指标记录器测试完成")

def test_analysis_plotter():
    """测试分析制图器功能"""
    print("🎨 测试DQN分析制图器...")
    
    # 检查是否有指标数据
    if os.path.exists("dqn_metrics"):
        plotter = DQNAnalysisPlotter("dqn_metrics", "test_analysis_plots")
        if plotter.all_metrics:
            plotter.generate_all_plots()
            print("✅ 分析制图器测试完成")
        else:
            print("⚠️ 没有找到指标数据，请先训练DQN变体")
    else:
        print("⚠️ dqn_metrics目录不存在，请先训练DQN变体")

def show_usage():
    """显示使用说明"""
    print("\n" + "="*80)
    print("🎯 DQN变体训练和分析系统使用说明")
    print("="*80)
    
    print("\n📋 已集成的DQN变体:")
    integrated_variants = [
        "DQN", "DoubleDQN", "DuelingDQN", "EWC_DQN", "LSTM_DQN", "PER_DQN", "RDQN", "Attention_DQN",
        "RDQN_NoImportance", "RDQN_NoMetaplasticity", "RDQN_UniformSync"
    ]
    for i, variant in enumerate(integrated_variants, 1):
        print(f"  {i:2d}. {variant}")
    
    print("\n🚀 训练步骤:")
    print("1. 手动训练各个DQN变体:")

    # 基础DQN变体
    basic_variants = ["DQN", "DoubleDQN", "DuelingDQN", "EWC_DQN", "LSTM_DQN", "PER_DQN", "RDQN", "Attention_DQN"]
    for variant in basic_variants:
        print(f"   cd {variant} && python train.py --epochs 50")

    # RDQN消融实验变体
    print("\n   # RDQN消融实验变体:")
    ablation_variants = ["RDQN_NoImportance", "RDQN_NoMetaplasticity", "RDQN_UniformSync"]
    for variant in ablation_variants:
        print(f"   cd RDQN_Ablation && python train.py --epochs 50 --model_class {variant}")
    
    print("\n2. 训练完成后，生成对比分析图表:")
    print("   python dqn_analysis_plotter.py")
    
    print("\n📊 输出文件:")
    print("   - dqn_metrics/: 各变体的训练指标数据")
    print("   - dqn_analysis_plots/: 对比分析图表")
    
    print("\n📈 生成的图表包括:")
    charts = [
        "reward_curves_comparison.png - 奖励曲线对比",
        "reward_stability_analysis.png - 奖励稳定性分析",
        "ap50_comparison.png - AP50性能对比", 
        "convergence_analysis.png - 收敛步数分析",
        "policy_entropy_analysis.png - 策略熵分析",
        "comprehensive_radar_chart.png - 综合性能雷达图",
        "dqn_comparison_report.txt - 文字总结报告"
    ]
    
    for chart in charts:
        print(f"   - {chart}")
    
    print("\n🔧 记录的指标:")
    metrics = [
        "reward曲线 - 每个epoch的奖励值",
        "reward std - 奖励波动情况（滑动窗口标准差）",
        "AP50/mAP - 检测精度（IoU>0.5的比例）",
        "收敛步数 - 达到奖励阈值所需epoch数",
        "策略熵 - 行为输出稳定性分析"
    ]
    
    for metric in metrics:
        print(f"   - {metric}")
    
    print("\n" + "="*80)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_metrics_recorder()
            test_analysis_plotter()
        elif sys.argv[1] == "plot":
            test_analysis_plotter()
        else:
            show_usage()
    else:
        show_usage()
