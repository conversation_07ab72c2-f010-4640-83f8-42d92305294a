import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import timm
import torchvision.transforms as T
from PIL import Image

from hilbert_mapping import ResidualEnhancedHilbertMapping
import pickle
import math
import torch.serialization

# Constants for the new DQN implementation
MEMORY_CAPACITY = 5000  # 记忆库大小
NUM_STATES = 25738  # 状态维度
NUM_ACTIONS = 9  # 动作总数 (0-7移动动作 + 8终止动作)
BATCH_SIZE = 128  # 批处理大小
LR = 1e-6  # 学习率
GAMMA = 0.9  # 奖励衰减因子




# 从net.py导入的初始化函数
def weights_init_kaiming(m):
    """
    对神经网络层进行Kaiming初始化
    """
    if isinstance(m, nn.Conv2d):
        nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
        if m.bias is not None:
            nn.init.constant_(m.bias, 0)
    elif isinstance(m, nn.Linear):
        nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
        nn.init.constant_(m.bias, 0)
    elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
        nn.init.constant_(m.weight, 1)
        nn.init.constant_(m.bias, 0)


# Dueling DQN网络架构 - 分离状态价值和动作优势
class DuelingQNet(nn.Module):
    """
    Dueling DQN网络架构
    核心思想：Q(s,a) = V(s) + A(s,a) - mean(A(s,a'))
    分离状态价值函数V(s)和动作优势函数A(s,a)
    """
    def __init__(self, state_dim=25738, hidden_dim=1024, action_dim=9):
        super(DuelingQNet, self).__init__()
        # 共享特征层
        self.feature_layer = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        # 状态价值流 V(s)
        self.value_stream = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 1)  # 输出标量状态价值
        )

        # 动作优势流 A(s,a)
        self.advantage_stream = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, action_dim)  # 输出每个动作的优势值
        )

    def forward(self, x):
        # 提取共享特征
        features = self.feature_layer(x)

        # 计算状态价值 V(s)
        value = self.value_stream(features)  # [batch_size, 1]

        # 计算动作优势 A(s,a)
        advantage = self.advantage_stream(features)  # [batch_size, 9]

        # Dueling架构核心：Q(s,a) = V(s) + A(s,a) - mean(A(s,a'))
        # 减去优势的均值以确保可识别性
        q_values = value + advantage - advantage.mean(dim=1, keepdim=True)

        # 应用softmax（保持与原始DQN一致）
        out = F.softmax(q_values, dim=1)
        return out

# 保持向后兼容的QNet别名
QNet = DuelingQNet

# Keep the old Net class for backward compatibility
class Net(nn.Module):
    """
    DQN的神经网络架构
    """

    def __init__(self, state_dim=2048):  # 修改为降维后的维度
        super(Net, self).__init__()
        self.fc1 = nn.Linear(state_dim, 1024)
        self.fc2 = nn.Linear(1024, 512)
        self.fc3 = nn.Linear(512, 9)  # 9个动作

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        action_value = self.fc3(x)
        action_value = F.softmax(action_value, dim=1)
        return action_value


# 新增：忆阻器调制层
class MemristorLayer(nn.Module):
    def __init__(self, input_dim, output_dim, decay_rate=0.01, importance_rate=0.01, activation_strength=0.1,
                 is_target=False):
        super(MemristorLayer, self).__init__()
        # 基本线性层参数
        self.weight = nn.Parameter(torch.Tensor(output_dim, input_dim))
        self.bias = nn.Parameter(torch.Tensor(output_dim))

        # 忆阻器特性参数 - 确保在与权重相同的设备上初始化
        self.register_buffer('memory_strength', torch.zeros_like(self.weight.data))
        self.register_buffer('importance', torch.ones_like(self.weight.data))

        # 配置
        self.decay_rate = decay_rate  # 目标网络较小，评估网络较大
        self.importance_rate = importance_rate  # 目标网络较小，评估网络较大
        self.activation_strength = activation_strength  # 控制激活对记忆的影响
        self.is_target = is_target

        # 初始化 - 使用Kaiming初始化提高稳定性
        nn.init.kaiming_uniform_(self.weight, a=math.sqrt(5))
        fan_in, _ = nn.init._calculate_fan_in_and_fan_out(self.weight)
        bound = 1 / math.sqrt(fan_in)
        nn.init.uniform_(self.bias, -bound, bound)

        # 用于跟踪参数变化的历史 - 也使用register_buffer确保设备一致
        self.register_buffer('param_change_history', torch.zeros_like(self.weight.data))
        self.momentum = 0.9

    def forward(self, input):
        # 计算有效权重（应用记忆强度调制）
        effective_weight = self.weight * torch.sigmoid(self.memory_strength * self.importance)

        # 应用线性变换 - 确保梯度传播
        output = F.linear(input, effective_weight, self.bias)

        # 在训练模式下更新记忆强度
        if self.training and not self.is_target:
            with torch.no_grad():
                # 计算激活强度（简化模型，实际可以使用更复杂的策略）
                batch_activation = torch.abs(input).mean(0, keepdim=True)  # [1, input_dim]
                weight_activation = torch.abs(self.weight)  # [output_dim, input_dim]

                # 更新记忆强度 - 确保设备一致性
                self.memory_strength += self.activation_strength * torch.matmul(
                    torch.ones(weight_activation.size(0), 1, device=input.device),
                    batch_activation
                ) * weight_activation

        return output

    def apply_decay(self):
        """应用记忆衰减"""
        with torch.no_grad():
            self.memory_strength *= (1 - self.decay_rate)

    def update_importance(self, td_error):
        """基于TD误差更新重要性"""
        with torch.no_grad():
            # 简化模型，实际中可能需要更精细的重要性计算
            importance_update = self.importance_rate * abs(td_error)
            self.importance += importance_update

    def update_metaplasticity(self, prev_weight):
        """更新萌发可塑性（记录参数变化）"""
        with torch.no_grad():
            current_change = torch.abs(self.weight.data - prev_weight)
            self.param_change_history = (
                    self.momentum * self.param_change_history +
                    (1 - self.momentum) * current_change
            )


# 特征提取器模型
class MobileFeatureExtractor(nn.Module):
    """
    移动网络特征提取器，用于提供RL状态表示和区域特征
    """

    def __init__(self, device='cuda'):
        super().__init__()
        # 使用RepVGG-A0作为特征提取器
        self.backbone = timm.create_model(
            'repvgg_a0',
            pretrained=True,
            features_only=True,
            out_indices=[3]  # 使用最后一个特征层
        ).to(device)

        # 设置为评估模式
        self.backbone.eval()

        # 冻结backbone参数
        for param in self.backbone.parameters():
            param.requires_grad = False

        # 获取特征维度
        self.device = device
        dummy = torch.zeros(1, 3, 128, 128).to(device)
        with torch.no_grad():
            test_output = self.backbone(dummy)[0]
            self.feat_channels = test_output.shape[1]
            self.feat_size = test_output.shape[2:]
            print(f"特征维度: {self.feat_channels}, 特征图大小: {self.feat_size}")

        # 计算展平后的特征维度
        self.flat_feat_dim = self.feat_channels * self.feat_size[0] * self.feat_size[1]
        print(f"展平后的特征维度: {self.flat_feat_dim}")

        # 添加希尔伯特映射层，用于降维
        self.original_state_dim = self.flat_feat_dim + 8 + 90  # 原始状态维度：特征+几何特征(8)+历史动作(90)
        self.hilbert_mapper = ResidualEnhancedHilbertMapping(
            input_dim=self.original_state_dim,
            output_dim=4096,  # 降维到4096
            gamma=0.01,  # 控制映射的平滑度
            residual_ratio=0.5  # 控制残差比例
        ).to(device)
        print(f"状态维度: {self.original_state_dim}")

        # RoIAlign精细调整组件 - 直接使用完整维度特征，不经过希尔伯特降维
        self.refine_head = nn.Sequential(
            nn.Conv2d(self.feat_channels, 256, 1),
            nn.ReLU(),
            nn.Conv2d(256, 64, 1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(64, 16),
            nn.ReLU(),
            nn.Linear(16, 4)
        ).to(device)

        # 预处理变换
        self.preprocess = T.Compose([
            T.Resize((128, 128)),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def extract_features(self, image, bbox):
        """提取特定边界框内的特征"""
        # 裁剪和预处理
        crop = image.crop((bbox[0], bbox[2], bbox[1], bbox[3]))
        input_tensor = self.preprocess(crop).unsqueeze(0).to(self.device)

        # 特征提取
        with torch.no_grad():
            features = self.backbone(input_tensor)[0]

        return features

    def get_full_features(self, features, bbox, image_size, history_actions):
        """获取未经希尔伯特空间降维的完整特征，用于边界框回归

        参数:
        - features: RepVGG提取的特征 [1, channels, height, width]
        - bbox: 边界框 [x1, x2, y1, y2]
        - image_size: 图像尺寸 (width, height)
        - history_actions: 历史动作

        返回:
        - full_features: 完整特征（不经过希尔伯特空间降维）
        """
        # 直接使用RepVGG提取的特征，不进行希尔伯特降维
        # 边界框回归模块直接使用这些特征
        return features

    def process_for_rl(self, features, bbox, image_size, history_actions):
        """处理特征用于RL决策，使用希尔伯特映射降维"""
        # 展平特征
        with torch.no_grad():
            flat_feats = features.reshape(1, -1)

            # 几何特征 - 8维
            width, height = image_size
            geometric_feats = torch.tensor([
                bbox[0] / width, bbox[1] / width, bbox[2] / height, bbox[3] / height,
                (bbox[1] - bbox[0]) / width, (bbox[3] - bbox[2]) / height,
                (bbox[0] + bbox[1]) / (2 * width), (bbox[2] + bbox[3]) / (2 * height)
            ], device=self.device).float()

            # 确保history_actions是numpy数组
            history_actions_np = np.array(history_actions, dtype=np.float32)
            history_actions_tensor = torch.tensor(history_actions_np, device=self.device)

            # 组合状态表示 - 特征向量 + 几何特征(8) + 历史动作(90)
            combined_state = torch.cat([
                flat_feats.reshape(-1),
                geometric_feats,
                history_actions_tensor
            ])

            # 使用希尔伯特映射降维
            mapped_state = self.hilbert_mapper(combined_state.unsqueeze(0)).squeeze(0)

            # 转换为numpy数组用于DQN
            rl_state = mapped_state.detach().cpu().numpy()

        return rl_state

    def refine_bbox(self, features, bbox):
        """基于特征精细调整边界框 - 直接使用完整特征，不经过希尔伯特降维"""
        # 在推理模式下运行，不需要梯度
        with torch.no_grad():
            # 检查refine_head是否可用
            if not hasattr(self, 'refine_head') or self.refine_head is None or len(
                    list(self.refine_head.parameters())) == 0:
                # 如果refine_head不可用，直接返回原始边界框
                return bbox.copy() if isinstance(bbox, list) else bbox.copy()

            # 使用完整特征(不经过希尔伯特降维)预测调整量
            # features已经是完整特征，不需要额外处理
            refinement = self.refine_head(features)  # [1, 4]

            # 计算原始边界框的宽度和高度
            width = bbox[1] - bbox[0]
            height = bbox[3] - bbox[2]

            # 计算调整后的边界框坐标
            refined_x1 = bbox[0] + refinement[0, 0].item() * width
            refined_x2 = bbox[1] + refinement[0, 1].item() * width
            refined_y1 = bbox[2] + refinement[0, 2].item() * height
            refined_y2 = bbox[3] + refinement[0, 3].item() * height

            # 确保坐标不会为负
            refined_x1 = max(0, refined_x1)
            refined_y1 = max(0, refined_y1)

            # 确保x2>x1, y2>y1，并至少有最小尺寸
            min_size = 5  # 最小尺寸（像素）
            if refined_x2 <= refined_x1 + min_size:
                refined_x2 = refined_x1 + min_size

            if refined_y2 <= refined_y1 + min_size:
                refined_y2 = refined_y1 + min_size

            refined_bbox = [refined_x1, refined_x2, refined_y1, refined_y2]

        return refined_bbox

    def calculate_target_offsets(self, current_bbox, gt_bbox):
        """
        计算当前边界框到真实边界框的目标偏移量

        参数:
        - current_bbox: [x1, x2, y1, y2] 当前预测的边界框
        - gt_bbox: [x1, x2, y1, y2] 真实边界框

        返回:
        - offsets: [dx1, dx2, dy1, dy2] 相对于当前边界框尺寸的偏移量
        """
        # 计算当前边界框的宽度和高度
        width = current_bbox[1] - current_bbox[0]
        height = current_bbox[3] - current_bbox[2]

        # 计算相对偏移量
        dx1 = (gt_bbox[0] - current_bbox[0]) / width
        dx2 = (gt_bbox[1] - current_bbox[1]) / width
        dy1 = (gt_bbox[2] - current_bbox[2]) / height
        dy2 = (gt_bbox[3] - current_bbox[3]) / height

        # 返回归一化的偏移量
        return [dx1, dx2, dy1, dy2]

    def load_refine_head(self, checkpoint_path):
        """加载预训练的refine_head权重

        参数:
        - checkpoint_path: 模型权重路径，可以是绝对路径或相对路径

        返回:
        - bool: 是否成功加载
        """
        if os.path.exists(checkpoint_path):
            try:
                # 加载权重
                checkpoint = torch.load(checkpoint_path, weights_only=False)

                # 过滤掉不需要的统计键
                if isinstance(checkpoint, dict):
                    if 'refine_head' in checkpoint:
                        filtered_dict = {k: v for k, v in checkpoint['refine_head'].items()
                                         if 'total_ops' not in k and 'total_params' not in k}
                    else:
                        filtered_dict = {k: v for k, v in checkpoint.items()
                                         if 'total_ops' not in k and 'total_params' not in k}

                    # 使用strict=False加载
                    self.refine_head.load_state_dict(filtered_dict, strict=False)
                    print(f"成功加载refine_head权重: {checkpoint_path}")
                    return True
                else:
                    print("模型格式不正确，无法加载")
                    return False
            except Exception as e:
                print(f"加载refine_head权重时出错: {e}")
                return False
        else:
            print(f"refine_head权重文件不存在: {checkpoint_path}")
            return False

    def save(self, prefix='best', save_dir='home'):
        """保存特征提取器和refine_head

        参数:
        - prefix: 保存文件前缀
        - save_dir: 保存目录

        返回:
        - bool: 是否成功保存
        """
        # 确保目录存在
        model_dir = os.path.join(save_dir, 'models')
        os.makedirs(model_dir, exist_ok=True)

        # 保存refine_head
        refine_head_path = os.path.join(model_dir, f'{prefix}_refine_head.pth')
        torch.save(self.refine_head.state_dict(), refine_head_path)

        print(f"特征提取器refine_head已保存到 {refine_head_path}")
        return True


# 辅助函数
def sigmoid(x):
    """Sigmoid函数，用于计算同步率"""
    return 1 / (1 + np.exp(-10 * x))  # 使用较陡的sigmoid函数


# 忆阻器启发的经验样本类
class MemristiveExperience:
    def __init__(self, state, action, reward, next_state, td_error=0.0):
        # 基础经验数据
        self.state = state  # 状态向量
        self.action = action  # 动作
        self.reward = reward  # 奖励
        self.next_state = next_state  # 下一状态

        # 忆阻器特性相关属性
        self.memory_strength = min(1.0, 0.3 + 0.7 * abs(td_error) / 10.0)  # 初始记忆强度
        self.age = 0  # 样本年龄
        self.last_used = 0  # 上次使用时间戳
        self.td_error_history = [abs(td_error)] if td_error != 0.0 else [0.0]  # TD误差历史
        self.use_count = 0  # 使用次数


# 忆阻器启发的时效经验回放池
class MemristiveReplayBuffer:
    def __init__(self, active_capacity=3000, archive_capacity=2000,
                 strength_threshold=0.2, decay_rate=0.001, enhancement_rate=0.1):
        # 缓冲区配置
        self.active_capacity = active_capacity  # 活跃区容量
        self.archive_capacity = archive_capacity  # 归档区容量
        self.active_buffer = []  # 活跃区样本列表
        self.archive_buffer = []  # 归档区样本列表

        # 忆阻器参数
        self.strength_threshold = strength_threshold  # 活跃区/归档区分界阈值
        self.decay_rate = decay_rate  # 记忆强度衰减率
        self.enhancement_rate = enhancement_rate  # 记忆强度增强率

        # 内部计数器和状态
        self.global_step = 0  # 全局步数
        self.total_samples = 0  # 总样本数
        self.active_sum_strength = 0.0  # 活跃区强度总和
        self.archive_sum_strength = 0.0  # 归档区强度总和

    def add(self, state, action, reward, next_state, td_error=0.0):
        """添加新样本到经验池"""
        # 创建新经验样本
        experience = MemristiveExperience(state, action, reward, next_state, td_error)

        # 确定添加位置
        if len(self.active_buffer) + len(self.archive_buffer) < self.active_capacity + self.archive_capacity:
            # 如果缓冲区未满，添加到活跃区
            self.active_buffer.append(experience)
            self.active_sum_strength += experience.memory_strength
        else:
            # 如果归档区有足够多样本且有低强度样本，替换归档区样本
            if len(self.archive_buffer) > 0:
                # 寻找归档区中记忆强度最低的样本
                min_idx = self._find_min_strength_index(self.archive_buffer)
                if self.archive_buffer[min_idx].memory_strength < experience.memory_strength:
                    self.archive_sum_strength -= self.archive_buffer[min_idx].memory_strength
                    self.archive_buffer[min_idx] = experience
                    self.archive_sum_strength += experience.memory_strength
                else:
                    # 如果新样本强度不足以替换，查找活跃区最弱样本
                    min_idx = self._find_min_strength_index(self.active_buffer)
                    if self.active_buffer[min_idx].memory_strength < experience.memory_strength:
                        self.active_sum_strength -= self.active_buffer[min_idx].memory_strength
                        self.active_buffer[min_idx] = experience
                        self.active_sum_strength += experience.memory_strength
            else:
                # 归档区为空，直接替换活跃区最弱样本
                min_idx = self._find_min_strength_index(self.active_buffer)
                self.active_sum_strength -= self.active_buffer[min_idx].memory_strength
                self.active_buffer[min_idx] = experience
                self.active_sum_strength += experience.memory_strength

        self.total_samples += 1

    def sample(self, batch_size, active_ratio=0.9):
        """基于记忆强度采样批次"""
        # 计算活跃区和归档区的采样数量
        active_size = min(int(batch_size * active_ratio), len(self.active_buffer))
        archive_size = min(batch_size - active_size, len(self.archive_buffer))

        # 构建活跃区采样概率
        active_probs = np.array([exp.memory_strength for exp in self.active_buffer])
        active_probs = active_probs / np.sum(active_probs) if np.sum(active_probs) > 0 else None

        # 构建归档区采样概率
        archive_probs = np.array([exp.memory_strength for exp in self.archive_buffer]) if len(
            self.archive_buffer) > 0 else None
        archive_probs = archive_probs / np.sum(archive_probs) if archive_probs is not None and np.sum(
            archive_probs) > 0 else None

        # 采样活跃区
        active_batch = []
        active_indices = []
        if active_size > 0 and len(self.active_buffer) > 0:
            active_indices = np.random.choice(len(self.active_buffer), active_size,
                                              p=active_probs, replace=False)
            active_batch = [self.active_buffer[i] for i in active_indices]

        # 采样归档区
        archive_batch = []
        archive_indices = []
        if archive_size > 0 and len(self.archive_buffer) > 0 and archive_probs is not None:
            archive_indices = np.random.choice(len(self.archive_buffer), archive_size,
                                               p=archive_probs, replace=False)
            archive_batch = [self.archive_buffer[i] for i in archive_indices]

        # 合并批次
        batch = active_batch + archive_batch
        batch_indices = [(0, i) for i in active_indices] + [(1, i) for i in archive_indices]

        # 更新采样样本的使用信息
        self.global_step += 1
        for exp in batch:
            exp.last_used = self.global_step
            exp.use_count += 1

        return batch, batch_indices

    def update_strengths(self, indices, td_errors):
        """根据TD误差更新样本记忆强度"""
        for (buffer_type, idx), td_error in zip(indices, td_errors):
            # 获取正确的缓冲区
            buffer = self.active_buffer if buffer_type == 0 else self.archive_buffer

            # 更新样本的TD误差历史
            buffer[idx].td_error_history.append(abs(td_error))
            if len(buffer[idx].td_error_history) > 5:
                buffer[idx].td_error_history.pop(0)

            # 更新记忆强度
            old_strength = buffer[idx].memory_strength
            # 基于TD误差增强记忆强度
            enhancement = self.enhancement_rate * abs(td_error)
            buffer[idx].memory_strength = min(1.0, buffer[idx].memory_strength + enhancement)

            # 更新强度总和
            if buffer_type == 0:
                self.active_sum_strength += (buffer[idx].memory_strength - old_strength)
            else:
                self.archive_sum_strength += (buffer[idx].memory_strength - old_strength)

    def apply_time_decay(self):
        """应用全局时效衰减并移动样本"""
        # 活跃区衰减与迁移
        samples_to_archive = []
        for i in range(len(self.active_buffer)):
            # 增加年龄
            self.active_buffer[i].age += 1

            # 应用衰减
            old_strength = self.active_buffer[i].memory_strength
            self.active_buffer[i].memory_strength *= (1.0 - self.decay_rate)
            self.active_sum_strength -= (old_strength - self.active_buffer[i].memory_strength)

            # 检查是否需要迁移到归档区
            if self.active_buffer[i].memory_strength < self.strength_threshold:
                samples_to_archive.append(i)

        # 从活跃区移动到归档区
        if len(samples_to_archive) > 0 and len(self.archive_buffer) < self.archive_capacity:
            # 从后向前移除，避免索引混乱
            for i in sorted(samples_to_archive, reverse=True):
                if len(self.archive_buffer) < self.archive_capacity:
                    exp = self.active_buffer.pop(i)
                    self.archive_buffer.append(exp)
                    self.active_sum_strength -= exp.memory_strength
                    self.archive_sum_strength += exp.memory_strength

        # 归档区衰减
        for i in range(len(self.archive_buffer)):
            self.archive_buffer[i].age += 1
            old_strength = self.archive_buffer[i].memory_strength
            self.archive_buffer[i].memory_strength *= (1.0 - self.decay_rate * 1.5)  # 归档区衰减更快
            self.archive_sum_strength -= (old_strength - self.archive_buffer[i].memory_strength)

    def reactivate_samples(self, count=50):
        """重新激活一些归档样本"""
        if len(self.archive_buffer) == 0 or len(self.active_buffer) >= self.active_capacity:
            return 0

        # 随机选择一些归档样本
        indices = np.random.choice(len(self.archive_buffer),
                                   min(count, len(self.archive_buffer)), replace=False)

        # 移动到活跃区
        moved_count = 0
        for i in sorted(indices, reverse=True):
            if len(self.active_buffer) < self.active_capacity:
                exp = self.archive_buffer.pop(i)
                # 提升其记忆强度作为重激活
                old_strength = exp.memory_strength
                exp.memory_strength = min(1.0, exp.memory_strength * 2.0)

                self.active_buffer.append(exp)
                self.archive_sum_strength -= old_strength
                self.active_sum_strength += exp.memory_strength
                moved_count += 1

        return moved_count

    def _find_min_strength_index(self, buffer):
        """查找缓冲区中记忆强度最低的样本索引"""
        if len(buffer) == 0:
            return None

        min_strength = float('inf')
        min_idx = 0

        for i, exp in enumerate(buffer):
            if exp.memory_strength < min_strength:
                min_strength = exp.memory_strength
                min_idx = i

        return min_idx

    def get_stats(self):
        """获取经验池状态统计信息"""
        stats = {
            'active_size': len(self.active_buffer),
            'archive_size': len(self.archive_buffer),
            'total_samples': self.total_samples,
            'active_avg_strength': self.active_sum_strength / max(1, len(self.active_buffer)),
            'archive_avg_strength': self.archive_sum_strength / max(1, len(self.archive_buffer)),
            'active_strengths': [exp.memory_strength for exp in self.active_buffer],
            'archive_strengths': [exp.memory_strength for exp in self.archive_buffer]
        }

        return stats

    def __len__(self):
        """返回经验池中样本总数"""
        return len(self.active_buffer) + len(self.archive_buffer)


# Dueling DQN类
class DuelingDQN:
    """
    Dueling DQN实现
    核心改进：分离状态价值函数V(s)和动作优势函数A(s,a)
    Q(s,a) = V(s) + A(s,a) - mean(A(s,a'))
    """

    def __init__(self, device, state_dim=None, memory_capacity=None, batch_size=None, lr=None, gamma=None, q_network_iteration=None):
        super().__init__()
        self.device = device

        # 使用传入的参数或默认值
        self.state_dim = state_dim if state_dim is not None else NUM_STATES
        self.memory_capacity = memory_capacity if memory_capacity is not None else MEMORY_CAPACITY
        self.batch_size = batch_size if batch_size is not None else BATCH_SIZE
        self.lr = lr if lr is not None else LR
        self.gamma = gamma if gamma is not None else GAMMA
        self.q_network_iteration = q_network_iteration

        # 初始化网络时立即过滤统计键
        self.eval_net = DuelingQNet(state_dim=self.state_dim)
        self.target_net = DuelingQNet(state_dim=self.state_dim)

        # 应用初始化权重
        self.eval_net.apply(self._filtered_weights_init)
        self.target_net.apply(self._filtered_weights_init)

        self.eval_net.to(self.device)
        self.target_net.to(self.device)
        self.learn_step_counter = 0  # 用这个记录学习了多少步
        self.train_loss = []
        self.learn_step = []
        self.memory_counter = 0  # 记录储存了多少条transition
        self.memory = np.zeros((self.memory_capacity, self.state_dim * 2 + 2), dtype=np.float32)  # 存储记忆的表
        self.optimizer = torch.optim.Adam(params=self.eval_net.parameters(), lr=self.lr)
        self.loss_func = nn.MSELoss()  # DQN损失函数

    def choose_action(self, state, EPISILO):  # 选择动作
        state = torch.unsqueeze(torch.FloatTensor(state), 0).to(self.device)
        # torch.FloatTensor()生成新的32位浮点数张量
        # torch.unsqueeze(x,0)输入一维张量，在第0维(行)扩展，第0维大小为1  torch.Size([1, 25112])
        if np.random.uniform() <= EPISILO:  # random policy 生成一个在[0, 1)内的随机数，如果小于等于EPSILON，随机选择动作
            action = np.random.randint(0, NUM_ACTIONS)  # np.random.randint用于生成一个指定范围内(0,6)的整数
        else:  # greedy policy 选择最优动作
            action_value = self.eval_net.forward(state)  # eval_net所输出得到的11个动作的q值
            action = torch.max(action_value, 1)[1].cpu().item()  # 取值最大的动作
            # .item()将Tensor变量转换为python标量
            # torch.max(x,1)取行的最大值及索引
        return action

    def store_transition(self, state, action, reward, next_state):  # 储存记忆
        transition = np.hstack((state, [action, reward], next_state))
        # numpy.hstack(tup) 参数tup可以是元组，列表，或者numpy数组，返回结果为按顺序堆叠numpy的数组（按列堆叠一个）
        index = self.memory_counter % self.memory_capacity  # 使用正确的内存容量
        # 总memory大小是固定的, 如果超出总大小, 取index为序数，旧memory就被新memory替换

    def _filtered_weights_init(self, m):
        if hasattr(m, 'weight') and 'total_ops' not in m._buffers:
            weights_init_kaiming(m)

    def learn(self):  # Dueling DQN学习算法 - 使用标准DQN学习规则

        # sample batch from all memory
        sample_index = np.random.choice(self.memory_capacity, self.batch_size)  # 在记忆中随机获取批次索引
        batch_memory = self.memory[sample_index, :]  # 抽取记忆表中的样本
        batch_state = torch.FloatTensor(batch_memory[:, :self.state_dim]).to(self.device)  # 当前状态
        batch_action = torch.LongTensor(batch_memory[:, self.state_dim:self.state_dim + 1].astype(int)).to(self.device)  # 动作
        batch_reward = torch.FloatTensor(batch_memory[:, self.state_dim + 1:self.state_dim + 2]).to(self.device)  # 奖励
        batch_next_state = torch.FloatTensor(batch_memory[:, self.state_dim + 2:2 * self.state_dim + 2]).to(self.device)  # 下一状态

        # Dueling DQN：使用Dueling架构的eval网络计算当前状态的Q值
        q_eval = self.eval_net(batch_state).gather(1, batch_action)  # 用Dueling eval_net获取batch中所执行动作的q值
        #  a.gather(1,index)  根据index参数（即是索引）返回数组里面对应位置的值,index的值表示是第几列,index值所在的行表示第几行

        # Dueling DQN：使用Dueling架构的target网络计算下一状态的最大Q值
        q_next = self.target_net(batch_next_state).detach()  # 用Dueling target_net获取下个状态每个动作的q值

        # 标准DQN学习算法：计算目标Q值 = reward + gamma * max(Q_target(s'))
        q_target_unterminated = batch_reward + self.gamma * q_next.max(1)[0].view(self.batch_size, 1)  # reward + gamma * maxQ(s_)
        # q_next.max(1)返回每行的最大值及其行坐标，[0]取值，[1]取索引

        # 处理终止状态：若action=8（触发动作），则目标值就是当前奖励
        q_target = torch.where(batch_action != 8, q_target_unterminated, batch_reward)
        # 若batch_action不等于8，q_target=q_target_unterminated; 若batch_action等于8,q_target=batch_reward

        # 计算损失并更新网络
        loss = self.loss_func(q_eval, q_target)
        # print(" {} step loss is {:.3f}".format(self.learn_step_counter, loss.cpu().detach().item()))

        self.train_loss.append(loss.cpu().detach().item())
        self.learn_step.append(self.learn_step_counter)

        self.optimizer.zero_grad()  # 将梯度归零
        loss.backward()  # 反向传播计算得到每个参数的梯度值
        self.optimizer.step()  # 通过梯度下降执行一步参数更新

        # Dueling DQN目标网络更新机制
        # 更新目标网络（过滤掉FLOPs统计键）
        self.learn_step_counter += 1
        if self.learn_step_counter % self.q_network_iteration == 0:
            # 过滤掉FLOPs统计键
            eval_state_dict = self.eval_net.state_dict()
            filtered_state_dict = {k: v for k, v in eval_state_dict.items()
                                 if 'total_ops' not in k and 'total_params' not in k}
            self.target_net.load_state_dict(filtered_state_dict)

    def save(self, prefix='best', save_dir='./DuelingDQN_models', hilbert_mapper=None):
        """保存Dueling DQN模型"""
        # 确保目录存在
        model_dir = os.path.join(save_dir, 'best_models')
        os.makedirs(model_dir, exist_ok=True)

        eval_net_path = os.path.join(model_dir, f'{prefix}_dqn_eval_net.pth')

        # 准备保存的状态字典
        save_dict = {
            'eval_net': self.eval_net.state_dict(),
            'target_net': self.target_net.state_dict(),
        }

        # 如果提供了希尔伯特映射器，将其保存到模型中
        if hilbert_mapper is not None:
            save_dict['hilbert_mapper'] = hilbert_mapper.state_dict()
            print(f"希尔伯特映射器权重已添加到DuelingDQN模型中")

        torch.save(save_dict, eval_net_path)
        print(f'DuelingDQN模型已保存到 {eval_net_path}')

    def load(self, save_dir='./DuelingDQN_models'):
        """加载Dueling DQN模型"""
        eval_net_path = os.path.join(save_dir, 'dueling_dqn_eval_net.pth')
        target_net_path = os.path.join(save_dir, 'dueling_dqn_target_net.pth')
        if os.path.exists(eval_net_path) and os.path.exists(target_net_path):
            # 加载并过滤掉统计键
            eval_state_dict = {k: v for k, v in torch.load(eval_net_path).items()
                               if k not in ['total_ops', 'total_params']}
            target_state_dict = {k: v for k, v in torch.load(target_net_path).items()
                                 if k not in ['total_ops', 'total_params']}

            self.eval_net.load_state_dict(eval_state_dict)
            self.target_net.load_state_dict(target_state_dict)
            print(f'Dueling DQN模型已从 {save_dir} 加载')
        else:
            print(f'模型文件不存在: {save_dir}')








def calculate_model_complexity(model_name, model, input_shape=None):
    if not has_calflops:
        print(f"无法计算{model_name}的FLOPs，需要安装calflops库")
        return

    try:
        if input_shape is None:
            # 默认使用一个小批量大小和常见的输入尺寸
            if isinstance(model, nn.Sequential) and "DQN" in model_name:
                # 针对DQN模型，输入形状应该是(batch_size, state_dim)
                input_shape = (1, NUM_STATES)
                # 创建一个具有正确形状的随机输入张量传递给calculate_flops
                dummy_input = torch.randn(input_shape)
                flops, macs, params = calculate_flops(
                    model=model,
                    args=[dummy_input],  # 使用args参数而不是input_shape
                    output_as_string=True,
                    output_precision=4,
                    print_results=True,
                    print_detailed=True
                )
            else:
                # 针对特征提取器，输入为图像
                input_shape = (1, 3, 128, 128)
                flops, macs, params = calculate_flops(
                    model=model,
                    input_shape=input_shape,
                    output_as_string=True,
                    output_precision=4,
                    print_results=True,
                    print_detailed=True
                )
        else:
            flops, macs, params = calculate_flops(
                model=model,
                input_shape=input_shape,
                output_as_string=True,
                output_precision=4,
                print_results=True,
                print_detailed=True
            )

        print(f"\n{model_name}模型复杂度: FLOPs={flops}, MACs={macs}, Params={params}\n")
    except Exception as e:
        print(f"计算{model_name}的FLOPs时出错: {e}")

        # 为避免中断训练，捕获异常后继续
        print(f"由于错误，跳过{model_name}的FLOPs计算，继续训练...") 