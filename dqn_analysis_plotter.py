#!/usr/bin/env python3
"""
DQN变体对比分析和制图脚本
读取所有DQN变体的训练数据，生成综合对比图表
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from dqn_metrics_recorder import DQNMetricsRecorder
import argparse

class DQNAnalysisPlotter:
    """DQN分析制图器"""
    
    def __init__(self, metrics_dir="dqn_metrics", output_dir="dqn_analysis_plots"):
        self.metrics_dir = metrics_dir
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置matplotlib样式
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 11,
            'figure.titlesize': 18,
            'font.family': 'serif'
        })
        
        # 加载所有指标数据
        self.all_metrics = DQNMetricsRecorder.get_all_metrics(metrics_dir)
        
        if not self.all_metrics:
            print("❌ 没有找到任何指标数据，请先训练DQN变体")
            return
        
        print(f"📊 成功加载 {len(self.all_metrics)} 个DQN变体的数据")
        print(f"📈 将生成对比分析图表到: {output_dir}")
    
    def plot_reward_curves(self):
        """绘制奖励曲线对比图"""
        plt.figure(figsize=(15, 8))
        
        colors = plt.cm.tab10(np.linspace(0, 1, len(self.all_metrics)))
        
        for i, (method_name, metrics) in enumerate(self.all_metrics.items()):
            if metrics.get('rewards'):
                epochs = metrics.get('epochs', list(range(1, len(metrics['rewards']) + 1)))
                rewards = metrics['rewards']
                
                plt.plot(epochs, rewards, 
                        label=method_name, 
                        color=colors[i], 
                        linewidth=2.5,
                        marker='o', 
                        markersize=4,
                        alpha=0.8)
        
        plt.xlabel('Training Epochs', fontweight='bold')
        plt.ylabel('Average Reward', fontweight='bold')
        plt.title('Training Reward Curves Comparison Across DQN Variants', fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图表
        plt.savefig(os.path.join(self.output_dir, 'reward_curves_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 奖励曲线对比图已保存")
    
    def plot_reward_stability(self):
        """绘制奖励稳定性分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 7))
        
        # 左图：奖励标准差曲线
        colors = plt.cm.tab10(np.linspace(0, 1, len(self.all_metrics)))
        
        for i, (method_name, metrics) in enumerate(self.all_metrics.items()):
            if metrics.get('reward_std_window'):
                epochs = metrics.get('epochs', list(range(1, len(metrics['reward_std_window']) + 1)))
                reward_std = metrics['reward_std_window']
                
                ax1.plot(epochs, reward_std, 
                        label=method_name, 
                        color=colors[i], 
                        linewidth=2,
                        marker='s', 
                        markersize=3)
        
        ax1.set_xlabel('Training Epochs', fontweight='bold')
        ax1.set_ylabel('Reward Standard Deviation', fontweight='bold')
        ax1.set_title('Reward Stability Over Training', fontweight='bold')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
        ax1.grid(True, alpha=0.3)
        
        # 右图：最终稳定性对比
        methods = []
        final_stds = []
        
        for method_name, metrics in self.all_metrics.items():
            if metrics.get('final_metrics', {}).get('final_reward_std') is not None:
                methods.append(method_name)
                final_stds.append(metrics['final_metrics']['final_reward_std'])
        
        if methods:
            bars = ax2.bar(methods, final_stds, color='lightcoral', alpha=0.7, edgecolor='darkred')
            ax2.set_title('Final Reward Stability Comparison', fontweight='bold')
            ax2.set_ylabel('Final Reward Std (Last 5 Epochs)', fontweight='bold')
            ax2.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, final_stds):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(final_stds)*0.01,
                        f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'reward_stability_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 奖励稳定性分析图已保存")
    
    def plot_ap50_comparison(self):
        """绘制AP50性能对比图"""
        methods = []
        final_ap50s = []
        max_ap50s = []
        
        for method_name, metrics in self.all_metrics.items():
            final_metrics = metrics.get('final_metrics', {})
            if 'final_ap50' in final_metrics:
                methods.append(method_name)
                final_ap50s.append(final_metrics['final_ap50'])
                max_ap50s.append(final_metrics.get('max_ap50', final_metrics['final_ap50']))
        
        if not methods:
            print("⚠️ 没有AP50数据，跳过AP50对比图")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 7))
        
        # 左图：最终AP50对比
        colors = plt.cm.viridis(np.linspace(0, 1, len(methods)))
        bars1 = ax1.bar(methods, final_ap50s, color=colors, alpha=0.8, edgecolor='black')
        ax1.set_title('Final AP50 Performance Comparison', fontweight='bold')
        ax1.set_ylabel('Final AP50 (IoU > 0.5 Ratio)', fontweight='bold')
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars1, final_ap50s):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(final_ap50s)*0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 添加平均线
        avg_ap50 = np.mean(final_ap50s)
        ax1.axhline(y=avg_ap50, color='red', linestyle='--', alpha=0.7, linewidth=2,
                    label=f'Average: {avg_ap50:.3f}')
        ax1.legend()
        ax1.grid(True, alpha=0.3, axis='y')
        
        # 右图：最高AP50对比
        bars2 = ax2.bar(methods, max_ap50s, color='lightgreen', alpha=0.8, edgecolor='darkgreen')
        ax2.set_title('Maximum AP50 Performance Comparison', fontweight='bold')
        ax2.set_ylabel('Maximum AP50 Achieved', fontweight='bold')
        ax2.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars2, max_ap50s):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(max_ap50s)*0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax2.grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'ap50_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ AP50性能对比图已保存")
    
    def plot_convergence_analysis(self):
        """绘制收敛步数分析图"""
        methods = []
        convergence_epochs = []
        total_epochs = []
        
        for method_name, metrics in self.all_metrics.items():
            final_metrics = metrics.get('final_metrics', {})
            methods.append(method_name)
            
            conv_epoch = metrics.get('convergence_epoch')
            if conv_epoch is not None:
                convergence_epochs.append(conv_epoch)
            else:
                convergence_epochs.append(final_metrics.get('total_epochs', 0))  # 未收敛则用总轮次
            
            total_epochs.append(final_metrics.get('total_epochs', len(metrics.get('epochs', []))))
        
        if not methods:
            print("⚠️ 没有收敛数据，跳过收敛分析图")
            return
        
        plt.figure(figsize=(14, 8))
        
        x = np.arange(len(methods))
        width = 0.35
        
        # 绘制收敛轮次和总轮次
        bars1 = plt.bar(x - width/2, convergence_epochs, width, 
                       label='Convergence Epoch', color='skyblue', alpha=0.8, edgecolor='navy')
        bars2 = plt.bar(x + width/2, total_epochs, width,
                       label='Total Epochs', color='lightcoral', alpha=0.8, edgecolor='darkred')
        
        plt.xlabel('DQN Variants', fontweight='bold')
        plt.ylabel('Number of Epochs', fontweight='bold')
        plt.title('Convergence Analysis Across DQN Variants', fontweight='bold')
        plt.xticks(x, methods, rotation=45, ha='right')
        plt.legend()
        
        # 添加数值标签
        for bar, value in zip(bars1, convergence_epochs):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(total_epochs)*0.01,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        for bar, value in zip(bars2, total_epochs):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(total_epochs)*0.01,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'convergence_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 收敛分析图已保存")
    
    def plot_policy_entropy(self):
        """绘制策略熵分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 7))
        
        # 左图：策略熵曲线
        colors = plt.cm.tab10(np.linspace(0, 1, len(self.all_metrics)))
        
        for i, (method_name, metrics) in enumerate(self.all_metrics.items()):
            if metrics.get('policy_entropy'):
                epochs = metrics.get('epochs', list(range(1, len(metrics['policy_entropy']) + 1)))
                entropy = metrics['policy_entropy']
                
                ax1.plot(epochs, entropy, 
                        label=method_name, 
                        color=colors[i], 
                        linewidth=2,
                        marker='^', 
                        markersize=3)
        
        ax1.set_xlabel('Training Epochs', fontweight='bold')
        ax1.set_ylabel('Policy Entropy', fontweight='bold')
        ax1.set_title('Policy Entropy Over Training', fontweight='bold')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
        ax1.grid(True, alpha=0.3)
        
        # 右图：最终策略熵对比
        methods = []
        final_entropies = []
        
        for method_name, metrics in self.all_metrics.items():
            final_metrics = metrics.get('final_metrics', {})
            if 'final_policy_entropy' in final_metrics:
                methods.append(method_name)
                final_entropies.append(final_metrics['final_policy_entropy'])
        
        if methods:
            bars = ax2.bar(methods, final_entropies, color='gold', alpha=0.8, edgecolor='orange')
            ax2.set_title('Final Policy Entropy Comparison', fontweight='bold')
            ax2.set_ylabel('Final Policy Entropy', fontweight='bold')
            ax2.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, final_entropies):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(final_entropies)*0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
            
            # 添加理想熵线（完全随机策略的熵）
            ideal_entropy = np.log(9)  # 9个动作的均匀分布熵
            ax2.axhline(y=ideal_entropy, color='red', linestyle='--', alpha=0.7, linewidth=2,
                        label=f'Random Policy: {ideal_entropy:.3f}')
            ax2.legend()
        
        ax2.grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'policy_entropy_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 策略熵分析图已保存")

    def plot_comprehensive_radar(self):
        """绘制综合性能雷达图"""
        # 收集所有方法的综合指标
        methods = []
        metrics_data = []

        for method_name, metrics in self.all_metrics.items():
            final_metrics = metrics.get('final_metrics', {})
            if not final_metrics:
                continue

            methods.append(method_name)

            # 归一化指标（0-1范围）
            method_scores = {}

            # 1. 最终奖励（越高越好）
            final_reward = final_metrics.get('final_reward', -2000)
            method_scores['Final Reward'] = max(0, (final_reward + 2000) / 2000)  # 假设-2000到0的范围

            # 2. 奖励稳定性（越低越好，所以取倒数）
            reward_std = final_metrics.get('final_reward_std', 1000)
            method_scores['Stability'] = max(0, 1 - reward_std / 1000)  # 标准差越小越好

            # 3. AP50性能（越高越好）
            ap50 = final_metrics.get('final_ap50', 0)
            method_scores['AP50'] = ap50  # 已经是0-1范围

            # 4. 收敛速度（越快越好）
            conv_epoch = metrics.get('convergence_epoch')
            total_epochs = final_metrics.get('total_epochs', 50)
            if conv_epoch:
                method_scores['Convergence Speed'] = max(0, 1 - conv_epoch / total_epochs)
            else:
                method_scores['Convergence Speed'] = 0  # 未收敛

            # 5. 策略稳定性（熵越低越好）
            policy_entropy = final_metrics.get('final_policy_entropy', np.log(9))
            method_scores['Policy Stability'] = max(0, 1 - policy_entropy / np.log(9))

            metrics_data.append(method_scores)

        if not methods:
            print("⚠️ 没有足够的数据绘制雷达图")
            return

        # 创建雷达图
        categories = list(metrics_data[0].keys())
        N = len(categories)

        # 计算角度
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # 闭合图形

        fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(projection='polar'))

        colors = plt.cm.tab10(np.linspace(0, 1, len(methods)))

        for i, (method, scores) in enumerate(zip(methods, metrics_data)):
            values = list(scores.values())
            values += values[:1]  # 闭合图形

            ax.plot(angles, values, 'o-', linewidth=2, label=method, color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])

        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories, fontsize=12)
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10)
        ax.grid(True)

        plt.title('Comprehensive Performance Radar Chart', size=16, fontweight='bold', pad=20)
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        plt.tight_layout()

        plt.savefig(os.path.join(self.output_dir, 'comprehensive_radar_chart.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 综合性能雷达图已保存")

    def generate_summary_report(self):
        """生成文字总结报告"""
        report_file = os.path.join(self.output_dir, 'dqn_comparison_report.txt')

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("DQN变体对比分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析的DQN变体数量: {len(self.all_metrics)}\n\n")

            # 1. 最终性能排名
            f.write("1. 最终奖励排名\n")
            f.write("-" * 40 + "\n")

            reward_ranking = []
            for method_name, metrics in self.all_metrics.items():
                final_reward = metrics.get('final_metrics', {}).get('final_reward', -9999)
                reward_ranking.append((method_name, final_reward))

            reward_ranking.sort(key=lambda x: x[1], reverse=True)
            for i, (method, reward) in enumerate(reward_ranking, 1):
                f.write(f"{i:2d}. {method:<20} : {reward:8.2f}\n")

            # 2. AP50性能排名
            f.write("\n2. AP50性能排名\n")
            f.write("-" * 40 + "\n")

            ap50_ranking = []
            for method_name, metrics in self.all_metrics.items():
                final_ap50 = metrics.get('final_metrics', {}).get('final_ap50', 0)
                if final_ap50 > 0:
                    ap50_ranking.append((method_name, final_ap50))

            ap50_ranking.sort(key=lambda x: x[1], reverse=True)
            for i, (method, ap50) in enumerate(ap50_ranking, 1):
                f.write(f"{i:2d}. {method:<20} : {ap50:8.4f}\n")

            # 3. 收敛速度排名
            f.write("\n3. 收敛速度排名\n")
            f.write("-" * 40 + "\n")

            convergence_ranking = []
            for method_name, metrics in self.all_metrics.items():
                conv_epoch = metrics.get('convergence_epoch')
                if conv_epoch:
                    convergence_ranking.append((method_name, conv_epoch))

            convergence_ranking.sort(key=lambda x: x[1])
            for i, (method, epoch) in enumerate(convergence_ranking, 1):
                f.write(f"{i:2d}. {method:<20} : {epoch:3d} epochs\n")

            # 4. 稳定性排名
            f.write("\n4. 奖励稳定性排名 (标准差越小越好)\n")
            f.write("-" * 50 + "\n")

            stability_ranking = []
            for method_name, metrics in self.all_metrics.items():
                reward_std = metrics.get('final_metrics', {}).get('final_reward_std', 9999)
                stability_ranking.append((method_name, reward_std))

            stability_ranking.sort(key=lambda x: x[1])
            for i, (method, std) in enumerate(stability_ranking, 1):
                f.write(f"{i:2d}. {method:<20} : {std:8.3f}\n")

            # 5. 详细统计
            f.write("\n5. 详细统计信息\n")
            f.write("-" * 50 + "\n")

            for method_name, metrics in self.all_metrics.items():
                f.write(f"\n{method_name}:\n")
                final_metrics = metrics.get('final_metrics', {})

                f.write(f"  最终奖励: {final_metrics.get('final_reward', 'N/A')}\n")
                f.write(f"  奖励稳定性: {final_metrics.get('final_reward_std', 'N/A')}\n")
                f.write(f"  最终AP50: {final_metrics.get('final_ap50', 'N/A')}\n")
                f.write(f"  最高AP50: {final_metrics.get('max_ap50', 'N/A')}\n")
                f.write(f"  收敛轮次: {metrics.get('convergence_epoch', '未收敛')}\n")
                f.write(f"  总训练轮次: {final_metrics.get('total_epochs', 'N/A')}\n")
                f.write(f"  最终策略熵: {final_metrics.get('final_policy_entropy', 'N/A')}\n")

        print(f"✅ 总结报告已保存: {report_file}")

    def generate_all_plots(self):
        """生成所有对比图表"""
        print(f"\n🎨 开始生成DQN变体对比分析图表...")
        print(f"📊 数据来源: {len(self.all_metrics)} 个DQN变体")

        # 生成所有图表
        self.plot_reward_curves()
        self.plot_reward_stability()
        self.plot_ap50_comparison()
        self.plot_convergence_analysis()
        self.plot_policy_entropy()
        self.plot_comprehensive_radar()

        # 生成总结报告
        self.generate_summary_report()

        print(f"\n🎉 所有分析图表生成完成!")
        print(f"📁 图表保存位置: {os.path.abspath(self.output_dir)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DQN变体对比分析和制图')
    parser.add_argument('--metrics_dir', type=str, default='dqn_metrics',
                       help='指标数据目录')
    parser.add_argument('--output_dir', type=str, default='dqn_analysis_plots',
                       help='图表输出目录')

    args = parser.parse_args()

    # 创建分析器并生成图表
    plotter = DQNAnalysisPlotter(args.metrics_dir, args.output_dir)

    if plotter.all_metrics:
        plotter.generate_all_plots()
    else:
        print("❌ 没有找到指标数据，请先训练DQN变体并记录数据")


if __name__ == "__main__":
    main()
