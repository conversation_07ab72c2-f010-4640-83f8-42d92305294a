import torch
import torch.nn as nn
import torchvision.transforms as transforms
import torchvision.models as models
import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from scipy.ndimage import gaussian_filter, maximum_filter, generate_binary_structure

# ====== Step 1: Load Image ======
img_path = r"D:\VOCdevkit\VOCdevkit\VOC2007\JPEGImages\000050.jpg"
img = Image.open(img_path).convert("RGB")
orig = np.array(img)

# ====== Step 2: Define Transform ======
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406],
                         std=[0.229, 0.224, 0.225])
])
input_tensor = transform(img).unsqueeze(0)  # [1, 3, 224, 224]

# ====== Step 3: Load Pretrained MobileNetV3 & Hook Feature ======
model = models.mobilenet_v3_large(pretrained=True)
model.eval()

feature_maps = None

def hook_fn(module, input, output):
    global feature_maps
    feature_maps = output.detach()

# Hook the last convolution layer
model.features[-1].register_forward_hook(hook_fn)

# ====== Step 4: Forward Pass ======
with torch.no_grad():
    _ = model(input_tensor)

# ====== Step 5: Generate Heatmap ======
# feature_maps shape: [1, C, H, W]
heatmap = torch.mean(feature_maps[0], dim=0).cpu().numpy()  # [H, W]
heatmap = np.maximum(heatmap, 0)
heatmap /= np.max(heatmap)  # normalize to [0,1]

# Resize to original image size
heatmap = cv2.resize(heatmap, (orig.shape[1], orig.shape[0]))

# ====== Step 6: Smooth + Extract Peaks ======
smoothed = gaussian_filter(heatmap, sigma=3)
neighborhood = generate_binary_structure(2, 2)
local_max = (smoothed == maximum_filter(smoothed, footprint=neighborhood))

# Top-K peaks
peak_coords = np.argwhere(local_max)
peak_values = [smoothed[y, x] for y, x in peak_coords]
top_k = 5
# Top-K peaks（使用 float 解决歧义）
top_peaks = sorted(
    [(float(smoothed[y, x]), (x, y)) for y, x in peak_coords],
    reverse=True
)[:top_k]
centers = [(int(x), int(y)) for _, (x, y) in top_peaks]
# ====== Step 7: Create Color Heatmap Overlay ======
heatmap_color = cv2.applyColorMap(np.uint8(255 * heatmap), cv2.COLORMAP_JET)
overlay = cv2.addWeighted(orig, 0.5, heatmap_color, 0.5, 0)

# ====== Step 8: Draw Keypoints ======
for cx, cy in centers:
    cv2.circle(overlay, (cx, cy), 8, (255, 255, 255), -1)

# ====== Step 9: Show ======
plt.figure(figsize=(10, 6))
plt.imshow(overlay[:, :, ::-1])
plt.title(f"Heatmap + Top {top_k} Centers: {centers}")
plt.axis('off')
plt.show()
