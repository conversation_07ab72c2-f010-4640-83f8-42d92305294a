import torch
import torchvision
from data import load_images_names_in_data_set, get_bb_of_gt_from_pascal_xml_annotation, \
    get_bb_of_gt_from_pascal_xml_annotationl, get_image_path
import torchvision.transforms as T
import torch.nn.functional as F
import torch.nn as nn
import numpy as np
import argparse
import os
from PIL import Image, ImageDraw, ImageFont
from net import Net, weights_init_kaiming
from utils import cal_iou, reward_func, IoUTrendTracker
import matplotlib.pyplot as plt
import cv2
import math
import time
import timm
from models import Attention_DQN, MobileFeatureExtractor
from thop import profile

# 添加父目录到路径以导入统一指标记录器
import sys
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from dqn_metrics_recorder import DQNMetricsRecorder

has_calflops = True
global NUM_STATES
BATCH_SIZE = 128  # 每次从记忆库中取的样本数量
LR = 1e-6  # 学习率
GAMMA = 0.9  # 奖励衰减因子
Lambda = 0.1
MEMORY_CAPACITY = 5000  # 记忆库大小
Q_NETWORK_ITERATION = 100  # 每100步更新一次target_net的参数
epochs = 50  # 轮次
NUM_ACTIONS = 9  # 动作总数 (0-7移动动作 + 8终止动作)
his_actions = 10  # 已执行的最后10个动作数
subscale = 0.1  # 放缩系数
NUM_STATES = 25738  # 状态维度 (updated for new DQN)
path_voc = r"D:\VOCdevkit\VOCdevkit"  # 数据集路径

np.random.seed(1)
torch.manual_seed(1)


# 新增：MobileNetV3特征提取器
class MobileFeatureExtractor(nn.Module):
    def __init__(self, device='cuda'):
        super().__init__()
        # 使用RepVGG-A0作为特征提取器
        self.backbone = timm.create_model(
            'repvgg_a0',
            pretrained=True,
            features_only=True,
            out_indices=[3]  # 使用最后一个特征层
        ).to(device)

        # 设置为评估模式
        self.backbone.eval()

        # 冻结backbone参数
        for param in self.backbone.parameters():
            param.requires_grad = False

        # 获取特征维度
        self.device = device
        dummy = torch.zeros(1, 3, 128, 128).to(device)
        with torch.no_grad():
            test_output = self.backbone(dummy)[0]
            self.feat_channels = test_output.shape[1]  # 通常是1280
            self.feat_size = test_output.shape[2:]  # 通常是8×8
            print(f"特征维度: {self.feat_channels}, 特征图大小: {self.feat_size}")

        # 计算展平后的特征维度
        self.flat_feat_dim = self.feat_channels * self.feat_size[0] * self.feat_size[1]
        print(f"展平后的特征维度: {self.flat_feat_dim}")

        # RoIAlign精细调整组件
        self.refine_head = nn.Sequential(
            nn.Conv2d(self.feat_channels, 256, 1),
            nn.ReLU(),
            nn.Conv2d(256, 64, 1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(64, 16),
            nn.ReLU(),
            nn.Linear(16, 4)
        ).to(device)

        # 预处理变换
        self.preprocess = T.Compose([
            T.Resize((128, 128)),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def extract_features(self, image, bbox):
        """提取特定边界框内的特征"""
        # 裁剪和预处理
        crop = image.crop((bbox[0], bbox[2], bbox[1], bbox[3]))
        input_tensor = self.preprocess(crop).unsqueeze(0).to(self.device)

        # 特征提取
        with torch.no_grad():
            features = self.backbone(input_tensor)[0]

        return features

    def process_for_rl(self, features, bbox, image_size, history_actions, hilbert_mapper=None):
        """处理特征用于RL决策"""
        # 展平特征
        with torch.no_grad():
            flat_feats = features.reshape(1, -1)  # [1, feat_channels*feat_size*feat_size]

            # 几何特征 - 8维
            width, height = image_size
            geometric_feats = torch.tensor([
                bbox[0] / width, bbox[1] / width, bbox[2] / height, bbox[3] / height,
                (bbox[1] - bbox[0]) / width, (bbox[3] - bbox[2]) / height,
                (bbox[0] + bbox[1]) / (2 * width), (bbox[2] + bbox[3]) / (2 * height)
            ], device=self.device).float()

            # 确保history_actions是numpy数组
            history_actions_np = np.array(history_actions, dtype=np.float32)
            history_actions_tensor = torch.tensor(history_actions_np, device=self.device)

            # 组合状态表示 - 特征向量 + 几何特征(8) + 历史动作(90)
            combined_state = torch.cat([
                flat_feats.reshape(-1),
                geometric_feats,
                history_actions_tensor
            ])

            # 如果提供了希尔伯特映射器，使用它来降低维度
            if hilbert_mapper is not None:
                # 使用希尔伯特映射进行降维
                mapped_state = hilbert_mapper(combined_state.unsqueeze(0)).squeeze(0).detach().cpu().numpy()
                return mapped_state
            else:
                # 否则直接返回原始状态
                return combined_state.detach().cpu().numpy()

    def refine_bbox(self, features, bbox):
        """基于特征精细调整边界框"""
        # 在推理模式下运行，不需要梯度
        with torch.no_grad():
            # 检查refine_head是否可用
            if not hasattr(self, 'refine_head') or self.refine_head is None or len(
                    list(self.refine_head.parameters())) == 0:
                # 如果refine_head不可用，直接返回原始边界框
                return bbox.copy() if isinstance(bbox, list) else bbox.copy()

            # 预测调整量
            refinement = self.refine_head(features)  # [1, 4]

            # 限制调整量的范围，防止过大的调整导致坐标错误
            # 将调整量限制在[-0.3, 0.3]的范围内
            # refinement = torch.clamp(refinement, -0.3, 0.3)

            # 计算原始边界框的宽度和高度
            width = bbox[1] - bbox[0]
            height = bbox[3] - bbox[2]

            # 计算调整后的边界框坐标
            refined_x1 = bbox[0] + refinement[0, 0].item() * width
            refined_x2 = bbox[1] + refinement[0, 1].item() * width
            refined_y1 = bbox[2] + refinement[0, 2].item() * height
            refined_y2 = bbox[3] + refinement[0, 3].item() * height

            # 确保坐标不会为负
            refined_x1 = max(0, refined_x1)
            refined_y1 = max(0, refined_y1)

            # 确保x2>x1, y2>y1，并至少有最小尺寸
            min_size = 5  # 最小尺寸（像素）
            if refined_x2 <= refined_x1 + min_size:
                refined_x2 = refined_x1 + min_size

            if refined_y2 <= refined_y1 + min_size:
                refined_y2 = refined_y1 + min_size

            refined_bbox = [refined_x1, refined_x2, refined_y1, refined_y2]

        return refined_bbox

    def calculate_target_offsets(self, current_bbox, gt_bbox):
        """
        计算当前边界框到真实边界框的目标偏移量

        参数:
        - current_bbox: [x1, x2, y1, y2] 当前预测的边界框
        - gt_bbox: [x1, x2, y1, y2] 真实边界框

        返回:
        - offsets: [dx1, dx2, dy1, dy2] 相对于当前边界框尺寸的偏移量
        """
        # 计算当前边界框的宽度和高度
        width = current_bbox[1] - current_bbox[0]
        height = current_bbox[3] - current_bbox[2]

        # 计算相对偏移量
        dx1 = (gt_bbox[0] - current_bbox[0]) / width
        dx2 = (gt_bbox[1] - current_bbox[1]) / width
        dy1 = (gt_bbox[2] - current_bbox[2]) / height
        dy2 = (gt_bbox[3] - current_bbox[3]) / height

        # 返回归一化的偏移量
        return [dx1, dx2, dy1, dy2]

    def load_refine_head(self, checkpoint_path):
        """加载预训练的refine_head权重"""
        if os.path.exists(checkpoint_path):
            try:
                # 尝试加载权重
                state_dict = torch.load(checkpoint_path, map_location=self.device, weights_only=False)

                # 如果是完整模型字典，提取refine_head部分
                if isinstance(state_dict, dict) and 'refine_head' in state_dict:
                    self.refine_head.load_state_dict(state_dict['refine_head'])
                # 如果是单独的refine_head权重
                else:
                    self.refine_head.load_state_dict(state_dict)

                print(f"成功加载refine_head权重: {checkpoint_path}")
                return True
            except Exception as e:
                print(f"加载refine_head权重时出错: {e}")
                return False
        else:
            print(f"refine_head权重文件不存在: {checkpoint_path}")
            return False

    def save(self, prefix='best', save_dir='home', hilbert_mapper=None):
        """保存特征提取器模型"""
        # 确保目录存在
        model_dir = os.path.join(save_dir, 'best_models')
        os.makedirs(model_dir, exist_ok=True)
        save_path = os.path.join(model_dir, f'{prefix}_feature_extractor.pth')

        # 准备保存的状态字典
        save_dict = {
            'backbone': self.backbone.state_dict(),
            'refine_head': self.refine_head.state_dict(),
            'feat_channels': self.feat_channels,
            'feat_size': self.feat_size,
            'flat_feat_dim': self.flat_feat_dim
        }

        # 如果提供了希尔伯特映射器，将其保存到模型中
        if hilbert_mapper is not None:
            save_dict['hilbert_mapper'] = hilbert_mapper.state_dict()
            print(f"希尔伯特映射器权重已添加到特征提取器模型中")

        torch.save(save_dict, save_path)
        print(f"特征提取器已保存到 {save_path}")

    def get_full_features(self, image, bbox):
        """提取完整特征，不经过希尔伯特空间降维"""
        # 裁剪和预处理
        crop = image.crop((bbox[0], bbox[2], bbox[1], bbox[3]))
        input_tensor = self.preprocess(crop).unsqueeze(0).to(self.device)

        # 特征提取
        with torch.no_grad():
            features = self.backbone(input_tensor)[0]

        return features


def init_process(image, transform=None):
    # image.show()
    # time.sleep(5)
    if transform:
        image = transform(image)
    return image.unsqueeze(0)  # 在第0位的位置添加一维


def inter_process(image, bbx, transform=None):
    (left, upper, right, lower) = (bbx[0], bbx[2], bbx[1], bbx[3])
    image_crop = image.crop((left, upper, right, lower))  # 使用image.crop()方法对图片进行切割
    # image_crop.show()
    # time.sleep(5)
    if transform:
        image_crop = transform(image_crop)
    return image_crop.unsqueeze(0)  # 在第0位的位置添加一维


def update_bbx(bbx, action):
    new_bbx = np.zeros(4)
    if action == 0:  # 左移
        new_bbx[0] = bbx[0] - (bbx[1] - bbx[0]) * subscale  # x1
        new_bbx[1] = bbx[1] - (bbx[1] - bbx[0]) * subscale  # x2
        new_bbx[2] = bbx[2]  # y1
        new_bbx[3] = bbx[3]  # y2
    elif action == 1:  # 右移
        new_bbx[0] = bbx[0] + (bbx[1] - bbx[0]) * subscale  # x1
        new_bbx[1] = bbx[1] + (bbx[1] - bbx[0]) * subscale  # x2
        new_bbx[2] = bbx[2]  # y1
        new_bbx[3] = bbx[3]  # y2
    elif action == 2:  # 上移
        new_bbx[0] = bbx[0]  # x1
        new_bbx[1] = bbx[1]  # x2
        new_bbx[2] = bbx[2] - (bbx[3] - bbx[2]) * subscale  # y1
        new_bbx[3] = bbx[3] - (bbx[3] - bbx[2]) * subscale  # y2
    elif action == 3:  # 下移
        new_bbx[0] = bbx[0]  # x1
        new_bbx[1] = bbx[1]  # x2
        new_bbx[2] = bbx[2] + (bbx[3] - bbx[2]) * subscale  # y1
        new_bbx[3] = bbx[3] + (bbx[3] - bbx[2]) * subscale  # y2
    elif action == 4:  # 水平压缩
        new_bbx[0] = bbx[0]  # x1
        new_bbx[1] = bbx[1]  # x2
        new_bbx[2] = bbx[2] + (bbx[3] - bbx[2]) * subscale * 1 / 2  # y1
        new_bbx[3] = bbx[3] - (bbx[3] - bbx[2]) * subscale * 1 / 2  # y2
    elif action == 5:  # 垂直压缩
        new_bbx[0] = bbx[0] + (bbx[1] - bbx[0]) * subscale * 1 / 2  # x1
        new_bbx[1] = bbx[1] - (bbx[1] - bbx[0]) * subscale * 1 / 2  # x2
        new_bbx[2] = bbx[2]  # y1
        new_bbx[3] = bbx[3]  # y2
        """
    elif action == 6:  # 水平扩展
        new_bbx[0] = bbx[0]  # x1
        new_bbx[1] = bbx[1]  # x2
        new_bbx[2] = bbx[2] - (bbx[3] - bbx[2]) * subscale  # y1
        new_bbx[3] = bbx[3] + (bbx[3] - bbx[2]) * subscale  # y2
    elif action == 7:  # 垂直扩展
        new_bbx[0] = bbx[0] - (bbx[1] - bbx[0]) * subscale  # x1
        new_bbx[1] = bbx[1] + (bbx[1] - bbx[0]) * subscale  # x2
        new_bbx[2] = bbx[2]  # y1
        new_bbx[3] = bbx[3]  # y2
        """
    elif action == 6:  # 中心压缩
        new_bbx[0] = bbx[0] + (bbx[1] - bbx[0]) * subscale * 1 / 2  # x1
        new_bbx[1] = bbx[1] - (bbx[1] - bbx[0]) * subscale * 1 / 2  # x2
        new_bbx[2] = bbx[2] + (bbx[3] - bbx[2]) * subscale * 1 / 2  # y1
        new_bbx[3] = bbx[3] - (bbx[3] - bbx[2]) * subscale * 1 / 2  # y2

    elif action == 7:  # 中心扩展
        new_bbx[0] = bbx[0] - (bbx[1] - bbx[0]) * subscale * 1 / 2  # x1
        new_bbx[1] = bbx[1] + (bbx[1] - bbx[0]) * subscale * 1 / 2  # x2
        new_bbx[2] = bbx[2] - (bbx[3] - bbx[2]) * subscale * 1 / 2  # y1
        new_bbx[3] = bbx[3] + (bbx[3] - bbx[2]) * subscale * 1 / 2  # y2

    elif action == 8:  # 终止/不变
        new_bbx = bbx

    return new_bbx


def update_history_actions(history_action, action):
    """更新历史动作"""
    action_vec = np.zeros(NUM_ACTIONS)
    action_vec[action] = 1.0
    return np.concatenate([history_action[NUM_ACTIONS:], action_vec])


class DIoULoss(nn.Module):
    """
    DIoU损失函数实现，适用于[x1, x2, y1, y2]格式的边界框
    """

    def __init__(self):
        super(DIoULoss, self).__init__()

    def forward(self, pred_offset, target_offset, anchors):
        """
        计算DIoU损失

        参数:
        - pred_offset: [batch_size, 4] 预测的偏移量 [dx1, dx2, dy1, dy2]
        - target_offset: [batch_size, 4] 目标偏移量 [dx1, dx2, dy1, dy2]
        - anchors: [batch_size, 4] 基础边界框 [x1, x2, y1, y2]

        返回:
        - DIoU损失值
        """
        batch_size = pred_offset.size(0)

        # 将偏移量转换为实际边界框坐标
        pred_boxes = []
        target_boxes = []

        for i in range(batch_size):
            # 使用detach()确保不会尝试对requires_grad的张量调用numpy()
            pred_off = pred_offset[i].detach().cpu().numpy()
            targ_off = target_offset[i].detach().cpu().numpy()
            anchor = anchors[i].detach().cpu().numpy()

            # 计算宽度和高度
            width = anchor[1] - anchor[0]
            height = anchor[3] - anchor[2]

            # 预测框坐标 (x1, y1, x2, y2格式)
            pred_x1 = anchor[0] + pred_off[0] * width
            pred_x2 = anchor[1] + pred_off[1] * width
            pred_y1 = anchor[2] + pred_off[2] * height
            pred_y2 = anchor[3] + pred_off[3] * height

            # 目标框坐标
            target_x1 = anchor[0] + targ_off[0] * width
            target_x2 = anchor[1] + targ_off[1] * width
            target_y1 = anchor[2] + targ_off[2] * height
            target_y2 = anchor[3] + targ_off[3] * height

            # 转换为[x1, y1, x2, y2]格式存储在列表中
            pred_boxes.append([pred_x1, pred_y1, pred_x2, pred_y2])
            target_boxes.append([target_x1, target_y1, target_x2, target_y2])

        # 转换为tensor
        device = pred_offset.device
        pred_boxes_np = np.array(pred_boxes, dtype=np.float32)
        target_boxes_np = np.array(target_boxes, dtype=np.float32)
        pred_boxes = torch.tensor(pred_boxes_np, device=device)
        target_boxes = torch.tensor(target_boxes_np, device=device)

        # 计算面积
        pred_area = (pred_boxes[:, 2] - pred_boxes[:, 0]) * (pred_boxes[:, 3] - pred_boxes[:, 1])
        target_area = (target_boxes[:, 2] - target_boxes[:, 0]) * (target_boxes[:, 3] - target_boxes[:, 1])

        # 计算交集
        inter_x1 = torch.max(pred_boxes[:, 0], target_boxes[:, 0])
        inter_y1 = torch.max(pred_boxes[:, 1], target_boxes[:, 1])
        inter_x2 = torch.min(pred_boxes[:, 2], target_boxes[:, 2])
        inter_y2 = torch.min(pred_boxes[:, 3], target_boxes[:, 3])

        # 计算交集面积，确保宽度和高度为正
        inter_w = torch.clamp(inter_x2 - inter_x1, min=0)
        inter_h = torch.clamp(inter_y2 - inter_y1, min=0)
        inter_area = inter_w * inter_h

        # 计算并集面积
        union_area = pred_area + target_area - inter_area

        # 计算IoU
        iou = inter_area / (union_area + 1e-6)

        # 计算外接矩形的坐标
        enclosing_x1 = torch.min(pred_boxes[:, 0], target_boxes[:, 0])
        enclosing_y1 = torch.min(pred_boxes[:, 1], target_boxes[:, 1])
        enclosing_x2 = torch.max(pred_boxes[:, 2], target_boxes[:, 2])
        enclosing_y2 = torch.max(pred_boxes[:, 3], target_boxes[:, 3])

        # 计算外接矩形的对角线距离
        enclosing_w = enclosing_x2 - enclosing_x1
        enclosing_h = enclosing_y2 - enclosing_y1
        c2 = enclosing_w.pow(2) + enclosing_h.pow(2) + 1e-6

        # 计算中心点距离
        pred_cx = (pred_boxes[:, 0] + pred_boxes[:, 2]) / 2
        pred_cy = (pred_boxes[:, 1] + pred_boxes[:, 3]) / 2
        target_cx = (target_boxes[:, 0] + target_boxes[:, 2]) / 2
        target_cy = (target_boxes[:, 1] + target_boxes[:, 3]) / 2

        center_dist2 = (pred_cx - target_cx).pow(2) + (pred_cy - target_cy).pow(2)

        # 计算DIoU
        diou = iou - center_dist2 / c2

        # 计算DIoU损失
        diou_loss = 1 - diou

        return diou_loss.mean()


class CornerLoss(nn.Module):
    """
    角点损失函数，特别关注边界框的四个角点位置
    """

    def __init__(self):
        super(CornerLoss, self).__init__()
        self.smooth_l1 = nn.SmoothL1Loss(reduction='none')

    def forward(self, pred_offset, target_offset):
        """
        计算角点损失

        参数:
        - pred_offset: [batch_size, 4] 预测的偏移量 [dx1, dx2, dy1, dy2]
        - target_offset: [batch_size, 4] 目标偏移量 [dx1, dx2, dy1, dy2]

        返回:
        - 角点损失值
        """
        # 计算每个偏移量的损失
        loss_per_corner = self.smooth_l1(pred_offset, target_offset)

        # 给予角点更高的权重
        # dx1, dx2, dy1, dy2分别对应左上、右上、左下、右下四个角点
        weights = torch.ones_like(loss_per_corner)
        weights[:, 0] = 1.2  # 左上角x坐标
        weights[:, 1] = 1.2  # 右上角x坐标
        weights[:, 2] = 1.2  # 左下角y坐标
        weights[:, 3] = 1.2  # 右下角y坐标

        # 应用权重
        weighted_loss = loss_per_corner * weights

        return weighted_loss.mean()


class CombinedBBoxLoss(nn.Module):
    """
    组合边界框损失，包括DIoU损失和角点损失
    """

    def __init__(self, diou_weight=0.7, corner_weight=0.3):
        super(CombinedBBoxLoss, self).__init__()
        self.diou_loss = DIoULoss()
        self.corner_loss = CornerLoss()
        self.diou_weight = diou_weight
        self.corner_weight = corner_weight

    def forward(self, pred_offset, target_offset, anchors):
        """
        计算组合损失

        参数:
        - pred_offset: [batch_size, 4] 预测的偏移量
        - target_offset: [batch_size, 4] 目标偏移量
        - anchors: [batch_size, 4] 基础边界框

        返回:
        - 组合损失值
        """
        diou_loss = self.diou_loss(pred_offset, target_offset, anchors)
        corner_loss = self.corner_loss(pred_offset, target_offset)

        # 组合损失
        return self.diou_weight * diou_loss + self.corner_weight * corner_loss


def calculate_model_complexity(model_name, model, input_shape=None, device='cuda'):
    """
    计算模型复杂度
    """
    try:
        if input_shape is None:
            input_shape = (1, NUM_STATES)
        # 确保输入张量在正确的设备上
        input_tensor = torch.randn(input_shape).to(device if torch.cuda.is_available() else 'cpu')
        # 确保模型在正确的设备上
        model_device = next(model.parameters()).device
        input_tensor = input_tensor.to(model_device)

        macs, params = profile(model, inputs=(input_tensor,), verbose=False)
        flops = macs * 2

        # 清理thop添加的属性，避免状态字典冲突
        def clean_thop_attributes(module):
            if hasattr(module, 'total_ops'):
                delattr(module, 'total_ops')
            if hasattr(module, 'total_params'):
                delattr(module, 'total_params')
            for child in module.children():
                clean_thop_attributes(child)

        clean_thop_attributes(model)

        return f"{flops / 1e9:.2f} GFLOPs", f"{macs / 1e9:.2f} GMACs", f"{params / 1e6:.2f} M"
    except Exception as e:
        print(f"计算{model_name}复杂度时出错: {e}")
        return None, None, None


def calculate_complete_model_flops(dqn_model, feature_extractor_model, hilbert_mapper=None, args=None):
    """
    计算整个模型的FLOPs总和，包括Attention_DQN、特征提取器和希尔伯特映射
    """
    print("\n===== 计算完整模型FLOPs =====")
    total_flops = 0
    total_macs = 0
    total_params = 0

    # 获取模型设备
    device = next(dqn_model.eval_net.parameters()).device

    # 计算Attention_DQN模型FLOPs
    print("1. 计算Attention_DQN评估网络...")
    dqn_flops, dqn_macs, dqn_params = calculate_model_complexity(
        "Attention_DQN评估网络",
        dqn_model.eval_net,
        input_shape=None,
        device=device
    )

    # 计算特征提取器backbone FLOPs
    print("2. 计算特征提取器backbone...")
    if hasattr(feature_extractor_model, 'backbone') and (args is None or args.flops_backbone):
        backbone_flops, backbone_macs, backbone_params = calculate_model_complexity(
            "Backbone",
            feature_extractor_model.backbone,
            input_shape=(1, 3, 128, 128),
            device=device
        )
    else:
        backbone_flops, backbone_macs, backbone_params = 0, 0, 0
        print("跳过backbone FLOPs计算")

    # 计算特征提取器refine_head FLOPs
    print("3. 计算特征提取器refine_head...")
    if hasattr(feature_extractor_model, 'refine_head'):
        refine_flops, refine_macs, refine_params = calculate_model_complexity(
            "Refine Head",
            feature_extractor_model.refine_head,
            input_shape=(1, feature_extractor_model.feat_channels,
                         feature_extractor_model.feat_size[0],
                         feature_extractor_model.feat_size[1]),
            device=device
        )
    else:
        refine_flops, refine_macs, refine_params = 0, 0, 0
        print("跳过refine_head FLOPs计算，模型没有refine_head")

    # 计算希尔伯特映射的FLOPs
    print("4. 计算希尔伯特映射层...")
    if hilbert_mapper is not None:
        # 希尔伯特映射的输入维度是特征维度+几何特征+历史动作
        input_dim = feature_extractor_model.flat_feat_dim + 8 + his_actions * NUM_ACTIONS

        # 计算希尔伯特映射的FLOPs
        hilbert_flops, hilbert_macs, hilbert_params = calculate_model_complexity(
            "希尔伯特映射",
            hilbert_mapper,
            input_shape=(1, input_dim),
            device=device
        )
    else:
        hilbert_flops, hilbert_macs, hilbert_params = 0, 0, 0
        print("跳过希尔伯特映射层FLOPs计算，未提供hilbert_mapper")

    # 尝试将字符串转换为数值以进行合计
    try:
        # 提取数值和单位
        def extract_value(val_str):
            if val_str is None:
                return 0
            if isinstance(val_str, (int, float)):
                return val_str

            # 字符串格式通常是: "123.45 GFLOPs"
            parts = val_str.split()
            if len(parts) != 2:
                return 0

            value = float(parts[0])
            unit = parts[1]

            # 转换到同一单位 (使用G作为基准)
            if 'T' in unit:
                value *= 1000
            elif 'M' in unit:
                value /= 1000
            elif 'K' in unit:
                value /= 1000000

            return value

        # 提取和转换各部分的FLOPs
        dqn_flops_val = extract_value(dqn_flops)
        backbone_flops_val = extract_value(backbone_flops)
        refine_flops_val = extract_value(refine_flops)
        hilbert_flops_val = extract_value(hilbert_flops)

        # 合计FLOPs (以G为单位)
        total_flops_val = dqn_flops_val + backbone_flops_val + refine_flops_val + hilbert_flops_val

        # 同样处理MACs和参数
        dqn_macs_val = extract_value(dqn_macs)
        backbone_macs_val = extract_value(backbone_macs)
        refine_macs_val = extract_value(refine_macs)
        hilbert_macs_val = extract_value(hilbert_macs)
        total_macs_val = dqn_macs_val + backbone_macs_val + refine_macs_val + hilbert_macs_val

        dqn_params_val = extract_value(dqn_params)
        backbone_params_val = extract_value(backbone_params)
        refine_params_val = extract_value(refine_params)
        hilbert_params_val = extract_value(hilbert_params)
        total_params_val = dqn_params_val + backbone_params_val + refine_params_val + hilbert_params_val

        # 格式化输出
        total_flops = f"{total_flops_val:.2f} GFLOPs"
        total_macs = f"{total_macs_val:.2f} GMACs"
        total_params = f"{total_params_val:.2f} M"

    except Exception as e:
        print(f"合计FLOPs时出错: {e}")
        total_flops = "未知"
        total_macs = "未知"
        total_params = "未知"

    print(f"\n===== 完整模型复杂度汇总 =====")
    print(f"总计: FLOPs={total_flops}, MACs={total_macs}, 参数数量={total_params}")
    print(f"- Attention_DQN评估网络: FLOPs={dqn_flops}, MACs={dqn_macs}, 参数数量={dqn_params}")
    if args is not None and args.flops_backbone:
        print(f"- 特征提取器Backbone: FLOPs={backbone_flops}, MACs={backbone_macs}, 参数数量={backbone_params}")
    print(f"- 特征提取器Refine Head: FLOPs={refine_flops}, MACs={refine_macs}, 参数数量={refine_params}")
    if hilbert_mapper is not None:
        print(f"- 希尔伯特映射: FLOPs={hilbert_flops}, MACs={hilbert_macs}, 参数数量={hilbert_params}")
    print("==============================\n")

    return total_flops, total_macs, total_params


def extract_value(val_str):
    if val_str is None:
        return 0
    if isinstance(val_str, (int, float)):
        return val_str

    # 字符串格式通常是: "123.45 GFLOPs"
    parts = val_str.split()
    if len(parts) != 2:
        return 0

    value = float(parts[0])
    unit = parts[1]

    # 转换到同一单位 (使用G作为基准)
    if 'T' in unit:
        value *= 1000
    elif 'M' in unit:
        value /= 1000
    elif 'K' in unit:
        value /= 1000000

    return value


def evaluate_model(dqn, feature_extractor, hilbert_mapper, image_names, device, save_dir, phase_name="evaluation",
                   test_voc_path=None, use_bbox_regression=True):
    """
    评估模型在给定数据集上的性能

    Args:
        dqn: Attention_DQN模型
        feature_extractor: 特征提取器
        hilbert_mapper: 希尔伯特映射器
        image_names: 评估数据集图像名称列表
        device: 使用的设备（CPU/GPU）
        save_dir: 结果保存目录
        phase_name: 评估阶段名称
        test_voc_path: 测试数据集路径
        use_bbox_regression: 是否使用边界框回归，默认True，如果提供则使用此路径

    Returns:
        average_iou: 平均IoU
        above_threshold: IoU>0.5的比例
        average_steps: 平均步数
    """
    print(f"===== 开始评估: {phase_name} =====")

    # 确保模型处于评估模式
    dqn.eval_net.eval()
    feature_extractor.eval()

    # 创建评估结果目录
    eval_dir = os.path.join(save_dir, f'evaluation_results/{phase_name}')
    os.makedirs(eval_dir, exist_ok=True)

    # 处理数据集，只保留有单个目标的图像
    single_target_image_names = []
    single_target_image_gts = []

    # 使用正确的VOC路径获取边界框
    voc_path_to_use = test_voc_path if test_voc_path is not None else path_voc
    print(f"使用评估数据路径: {voc_path_to_use}")

    for image_name in image_names:
        annotation = get_bb_of_gt_from_pascal_xml_annotation(image_name, voc_path_to_use)
        if len(annotation) > 1:  # 跳过有多个目标的图片
            continue
        if len(annotation) == 0:  # 跳过没有目标的图片
            continue
        single_target_image_names.append(image_name)
        single_target_image_gts.append(annotation[0][1:])  # [x1,x2,y1,y2]

    print(f"有效评估图像数量: {len(single_target_image_names)}")

    # 如果没有有效图像，提前返回
    if len(single_target_image_names) == 0:
        print("警告：没有找到有效的评估图像，请检查数据集路径")
        return 0, 0, 0

    # 评估指标
    total_ious = []
    total_steps = []  # 添加步数统计
    iou_above_threshold = 0

    # 遍历每张图像进行评估
    for index, image_name in enumerate(single_target_image_names):
        if index % 10 == 0:
            print(f"评估进度: {index}/{len(single_target_image_names)}")

        image_path = get_image_path(image_name, voc_path_to_use)
        if image_path is None:
            print(f"警告：找不到图像文件: {image_name}.jpg")
            continue

        try:
            image_original = Image.open(image_path)
        except FileNotFoundError:
            print(f"警告：找不到图像文件: {image_path}")
            continue

        width, height = image_original.size

        # 初始化边界框为整个图像
        bbx = [0, width, 0, height]  # [x1, x2, y1, y2]
        gt_bbx = single_target_image_gts[index].copy()

        # 初始化历史动作
        history_action = np.zeros(his_actions * NUM_ACTIONS)

        # 记录步骤中的最高IoU
        max_iou = 0
        best_bbx = bbx.copy()
        actual_steps = 0  # 记录实际执行的步数

        # 最多50步探索
        for step in range(50):
            actual_steps = step + 1  # 更新实际步数
            # 提取特征
            features = feature_extractor.extract_features(image_original, bbx)

            # 处理特征用于RL决策
            state = feature_extractor.process_for_rl(
                features, bbx,
                (width, height),
                history_action,
                hilbert_mapper
            )

            # 使用模型选择动作（评估模式，EPISILO=0不进行随机探索）
            with torch.no_grad():
                action = dqn.choose_action(
                    state,
                    EPISILO=0
                )

            # 如果选择的动作是终止，则结束当前图像的评估
            if action == 8:
                break

            # 更新边界框
            new_bbx = update_bbx(bbx, action)

            # 计算IoU
            iou = cal_iou(new_bbx, gt_bbx)

            # 更新最高IoU和最佳边界框
            if iou > max_iou:
                max_iou = iou
                best_bbx = new_bbx.copy()

            # 更新当前边界框和历史动作
            bbx = new_bbx
            history_action = update_history_actions(history_action, action)

        # 应用精细调整（如果有且启用）
        if use_bbox_regression and hasattr(feature_extractor, 'refine_head') and feature_extractor.refine_head is not None:
            # 提取特征
            features = feature_extractor.extract_features(image_original, best_bbx)
            # 精细调整边界框
            refined_bbx = feature_extractor.refine_bbox(features, best_bbx)
            # 计算精调后的IoU
            refined_iou = cal_iou(refined_bbx, gt_bbx)
            # 如果精调后的IoU更高，则使用精调后的边界框
            if refined_iou > max_iou:
                max_iou = refined_iou
                best_bbx = refined_bbx.copy()

        # 记录最终IoU和步数
        total_ious.append(max_iou)
        total_steps.append(actual_steps)  # 记录实际步数
        if max_iou > 0.5:
            iou_above_threshold += 1

        # 可视化结果（可选）
        # 此处可以添加将结果保存为图像的代码

    # 计算评估指标
    average_iou = np.mean(total_ious) if total_ious else 0
    average_steps = np.mean(total_steps) if total_steps else 0  # 计算平均步数
    above_threshold = iou_above_threshold / len(total_ious) if total_ious else 0

    # 打印评估结果
    print(f"===== 评估结果: {phase_name} =====")
    print(f"平均IoU: {average_iou:.4f}")
    print(f"平均步数: {average_steps:.2f}")  # 添加平均步数输出
    print(f"IoU>0.5比例: {above_threshold:.4f}")
    print(f"有效评估样本数: {len(total_ious)}")
    print(f"边界框回归: {'启用' if use_bbox_regression else '禁用'}")  # 显示边界框回归状态

    # 保存评估结果
    result_file = os.path.join(eval_dir, f"results.txt")
    with open(result_file, 'w') as f:
        f.write(f"平均IoU: {average_iou:.4f}\n")
        f.write(f"平均步数: {average_steps:.2f}\n")  # 添加平均步数保存
        f.write(f"IoU>0.5比例: {above_threshold:.4f}\n")
        f.write(f"有效评估样本数: {len(total_ious)}\n")
        f.write(f"边界框回归: {'启用' if use_bbox_regression else '禁用'}\n")  # 保存边界框回归状态

        # 保存每个样本的IoU和步数
        f.write("\n每个样本的IoU和步数:\n")
        for i, (image_name, iou, steps) in enumerate(zip(single_target_image_names[:len(total_ious)], total_ious, total_steps)):
            f.write(f"{image_name}: IoU={iou:.4f}, 步数={steps}\n")

    return average_iou, above_threshold, average_steps


def main(args):
    global NUM_STATES
    # Class category of PASCAL that the RL agent will be searching
    device = torch.device("cuda:0" if (torch.cuda.is_available() and args.use_gpu) else "cpu")

    # 设置测试集路径
    test_voc_path = args.test_voc_path if args.test_voc_path else path_voc

    # 加载训练集图像名称
    image_names = load_images_names_in_data_set('aeroplane_trainval', path_voc)

    # 加载评估集图像名称
    if args.eval_only:
        print(f"===== 仅评估模式 =====")
        print(f"使用数据集: {args.eval_set}")
        eval_image_names = load_images_names_in_data_set(args.eval_set, args.test_voc_path)
        print(f"评估集图像数量: {len(eval_image_names)}")
    else:
        # 如果不是仅评估模式，在训练结束后也要加载评估数据集进行评估
        eval_set_path = args.test_voc_path if args.test_voc_path else path_voc
        print(f"使用评估数据路径: {eval_set_path}")
        print(f"加载评估数据集: {args.eval_set}")
        eval_image_names = load_images_names_in_data_set(args.eval_set, eval_set_path)

    # 创建保存模型的目录
    os.makedirs(os.path.join(args.save_dir, 'models'), exist_ok=True)
    os.makedirs(os.path.join(args.save_dir, 'best_models'), exist_ok=True)

    # 实例化特征提取器
    feature_extractor = MobileFeatureExtractor(device)
    # 确保特征提取器处于评估模式
    feature_extractor.eval()

    # 计算原始特征维度（用于希尔伯特映射的输入）
    original_state_dim = feature_extractor.flat_feat_dim + 8 + his_actions * NUM_ACTIONS
    print(f"原始状态维度: {original_state_dim}")

    # 创建希尔伯特映射器
    from hilbert_mapping import ResidualEnhancedHilbertMapping
    # 希尔伯特映射的输入维度是特征维度+几何特征+历史动作
    input_dim = original_state_dim
    # 希尔伯特映射的输出维度设为1024，这是降维后的特征维度
    output_dim = 4096  # 希尔伯特空间降维后的维度

    hilbert_mapper = ResidualEnhancedHilbertMapping(
        input_dim=input_dim,
        output_dim=output_dim,
        gamma=0.01,  # 默认为0.01
        residual_ratio=0.5  # 默认为0.1
    ).to(device)

    # 打印希尔伯特映射器的结构
    print("===== 希尔伯特映射器结构 =====")
    print(hilbert_mapper)
    print(f"希尔伯特映射器参数数量: {sum(p.numel() for p in hilbert_mapper.parameters() if p.requires_grad)}")

    # 更新NUM_STATES为希尔伯特映射后的维度
    NUM_STATES = output_dim
    print(f"希尔伯特映射: 输入维度={input_dim}, 输出维度={NUM_STATES}")

    single_plane_image_names = []
    single_plane_image_gts = []

    # 创建Attention_DQN，传入希尔伯特降维后的状态维度
    print("使用忆阻器增强的目标网络Attention_DQN")
    dqn = Attention_DQN(device, state_dim=NUM_STATES, memory_capacity=MEMORY_CAPACITY,
                        batch_size=BATCH_SIZE, lr=LR, gamma=GAMMA,
                        q_network_iteration=Q_NETWORK_ITERATION)

    # 初始化统一指标记录器
    metrics_recorder = DQNMetricsRecorder("Attention_DQN", save_dir="dqn_metrics")
    print(f"📊 Attention_DQN指标记录器初始化完成")

    # 加载完整模型（优先）
    if args.load_model:
        if os.path.exists(args.load_model):
            try:
                # 加载完整模型字典
                complete_model = torch.load(args.load_model, map_location=device, weights_only=False)
                if isinstance(complete_model, dict):
                    if 'dqn' in complete_model:
                        # 创建新的状态字典，将旧的键映射到新的键
                        new_state_dict = {}
                        old_state_dict = complete_model['dqn']
                        # 过滤掉统计键
                        old_state_dict = {k: v for k, v in old_state_dict.items()
                                          if 'total_ops' not in k and 'total_params' not in k}

                        key_mapping = {
                            'l1.weight': 'fc1.weight',
                            'l1.bias': 'fc1.bias',
                            'l2.weight': 'fc2.weight',
                            'l2.bias': 'fc2.bias',
                            'l3.weight': 'fc3.weight',
                            'l3.bias': 'fc3.bias'
                        }
                        for old_key, new_key in key_mapping.items():
                            if old_key in old_state_dict:
                                new_state_dict[new_key] = old_state_dict[old_key]

                        # 加载新的状态字典
                        dqn.eval_net.load_state_dict(new_state_dict, strict=False)
                        print(f"从完整模型加载Attention_DQN权重成功")
                    if 'refine_head' in complete_model:
                        # 过滤掉统计键
                        filtered_dict = {k: v for k, v in complete_model['refine_head'].items()
                                         if 'total_ops' not in k and 'total_params' not in k}
                        feature_extractor.refine_head.load_state_dict(filtered_dict, strict=False)
                        print(f"从完整模型加载边界框回归权重成功")
                    print(f"成功加载完整模型: {args.load_model}")
                else:
                    print(f"无效的模型格式: {args.load_model}")
            except Exception as e:
                print(f"加载完整模型时出错: {e}")
    else:
        # 加载Attention_DQN预训练权重
        if args.load_dqn:
            print(f"加载Attention_DQN权重: {args.load_dqn}")
            dqn.load(args.load_dqn)

        # 加载refine_head预训练权重
        if args.load_refine:
            print(f"加载refine_head权重: {args.load_refine}")
            try:
                # 尝试加载权重
                state_dict = torch.load(args.load_refine, map_location=device, weights_only=False)

                # 过滤掉不需要的统计键
                if isinstance(state_dict, dict):
                    if 'refine_head' in state_dict:
                        filtered_dict = {k: v for k, v in state_dict['refine_head'].items()
                                         if 'total_ops' not in k and 'total_params' not in k}
                    else:
                        filtered_dict = {k: v for k, v in state_dict.items()
                                         if 'total_ops' not in k and 'total_params' not in k}

                    # 使用strict=False加载
                    feature_extractor.refine_head.load_state_dict(filtered_dict, strict=False)
                    print(f"成功加载refine_head权重: {args.load_refine}")
                else:
                    print(f"refine_head模型格式不正确，无法加载")
            except Exception as e:
                print(f"加载refine_head权重时出错: {e}")

    EPISILO = args.EPISILO  # e-贪婪系数

    # 处理单一目标图像
    for image_name in image_names:
        annotation = get_bb_of_gt_from_pascal_xml_annotation(image_name, path_voc)
        if annotation.shape[0] > 1:  # 使用shape[0]而不是len()检查矩阵行数
            continue  # 跳过有多个目标的图片

        if annotation.shape[0] > 0:  # 确保至少有一个目标
            single_plane_image_names.append(image_name)  # 获取只有单个目标的图片名
            single_plane_image_gts.append(annotation[0][1:])  # [[x1,x2,y1,y2] ...] 获取单张飞机图片的飞机目标框

    # 定义阶段标志
    PHASE_RL = 0
    PHASE_REFINE = 1
    PHASE_JOINT = 2

    # 设置每个阶段的轮数
    rl_epochs = args.rl_epochs if not args.skip_rl else 0  # 如果skip_rl为True，则设为0
    refine_epochs = args.refine_epochs if not args.skip_refine else 0
    joint_epochs = args.joint_epochs if not args.skip_joint else 0

    # 创建结果目录
    os.makedirs(os.path.join(args.save_dir, 'visualization/visualization_aeroplane_train'), exist_ok=True)
    os.makedirs(os.path.join(args.save_dir, 'visualization/loss'), exist_ok=True)
    os.makedirs(os.path.join(args.save_dir, 'models'), exist_ok=True)

    # 总训练轮数
    total_epochs = rl_epochs + refine_epochs + joint_epochs

    # 初始化最佳性能变量 - 用于跨阶段追踪
    best_rl_reward = float('-inf')
    best_refine_iou = float('-inf')
    best_joint_iou = float('-inf')
    best_joint_reward = float('-inf')

    # 计算模型的FLOPs、MACs和参数数量
    if args.compute_flops:
        calculate_complete_model_flops(dqn, feature_extractor, hilbert_mapper, args)

    # 仅评估模式
    if args.eval_only:
        print("===== 仅评估模式，跳过训练 =====")
        # 确保模型在评估前已加载权重
        if not args.load_model and not args.load_dqn:
            print("警告: 在评估模式下未加载任何模型权重，结果可能不准确")

        # 进行评估
        avg_iou, above_threshold, avg_steps = evaluate_model(
            dqn,
            feature_extractor,
            hilbert_mapper,
            eval_image_names,
            device,
            args.save_dir,
            phase_name=args.eval_set,
            test_voc_path=args.test_voc_path,
            use_bbox_regression=not args.no_bbox_regression
        )

        # 打印最终结果
        print("\n===== 最终评估结果 =====")
        print(f"数据集: {args.eval_set}")
        print(f"平均IoU: {avg_iou:.4f}")
        print(f"平均步数: {avg_steps:.2f}")
        print(f"IoU>0.5比例: {above_threshold:.4f}")
        print(f"边界框回归: {'禁用' if args.no_bbox_regression else '启用'}")

        # 结束程序
        return

    # 阶段1: RL训练
    if rl_epochs > 0 and not args.skip_rl:
        print("===== 阶段1: RL训练 =====")

        # 计算Attention_DQN模型复杂度
        if args.compute_flops:
            print("计算Attention_DQN评估网络的复杂度...")
            calculate_model_complexity("Attention_DQN评估网络", dqn.eval_net)

        # RL训练进行rl_epochs轮
        for i in range(rl_epochs):
            current_phase = PHASE_RL

            # 计算当前训练进度(0~1之间)
            training_progress = i / (rl_epochs - 1) if rl_epochs > 1 else 1.0

            ep_reward = 0  # episode reword
            epoch_actions = []  # 收集本epoch的所有动作用于计算策略熵

            # 经验衰减计数器
            decay_counter = 0

            for index, image_name in enumerate(single_plane_image_names):
                image_path = get_image_path(image_name, path_voc)
                if image_path is None:
                    print(f"警告：找不到图像文件: {image_name}.jpg")
                    continue
                image_original = Image.open(image_path)
                width, height = image_original.size  # 宽和高

                # 图片中探索飞机目标
                bbx = [0, width, 0, height]  # [x1, x2, y1, y2]
                gt_bbx = single_plane_image_gts[index].copy()

                # 初始化历史动作
                history_action = np.zeros(his_actions * NUM_ACTIONS)  # 已执行的最后his_actions个动作(one-hot编码)

                step = 0  # 步数计数器
                epoch_reward = 0  # 当前图片的奖励总和

                while step < 50:  # 最多50步
                    # 提取特征
                    features = feature_extractor.extract_features(image_original, bbx)
                    state = feature_extractor.process_for_rl(features, bbx, (width, height), history_action,
                                                             hilbert_mapper)

                    # 选择动作
                    action = dqn.choose_action(state, EPISILO)
                    epoch_actions.append(action)  # 收集动作用于策略熵计算

                    # 根据动作更新边界框
                    new_bbx = update_bbx(bbx, action)

                    # 计算奖励
                    reward = reward_func(bbx, new_bbx, gt_bbx, action, training_progress=training_progress)
                    ep_reward += reward
                    epoch_reward += reward

                    # 提取新状态
                    new_features = feature_extractor.extract_features(image_original, new_bbx)
                    new_history_action = update_history_actions(history_action, action)
                    next_state = feature_extractor.process_for_rl(new_features, new_bbx, (width, height),
                                                                  new_history_action, hilbert_mapper)

                    # 存储经验
                    dqn.store_transition(state, action, reward, next_state)

                    # 学习
                    if dqn.memory_counter >= BATCH_SIZE:
                        dqn.learn()

                    # 判断是否达到终止条件
                    if action == 8 or step == 49:  # 动作8是终止
                        # 可视化最后一步的结果
                        if i == rl_epochs - 1:  # 最后一个epoch
                            draw = ImageDraw.Draw(image_original)
                            draw.rectangle([bbx[0], bbx[2], bbx[1], bbx[3]], outline='red', width=2)  # 预测框
                            draw.rectangle([gt_bbx[0], gt_bbx[2], gt_bbx[1], gt_bbx[3]], outline='green',
                                           width=2)  # 真实框

                            # 计算IOU
                            iou = cal_iou(new_bbx, gt_bbx)
                            font = ImageFont.truetype('arial.ttf', 20)
                            draw.text([bbx[0], bbx[2]], f'IOU: {iou:.2f}', fill=(255, 0, 0), font=font)

                            # 保存可视化结果
                            vis_path = os.path.join(args.save_dir, 'visualization', 'visualization_aeroplane_train', f'rl_{image_name}.jpg')
                            os.makedirs(os.path.dirname(vis_path), exist_ok=True)
                            image_original.save(vis_path)
                        break

                    bbx = new_bbx
                    history_action = new_history_action
                    step += 1

                # 基础Attention_DQN不需要经验时效衰减（这是RDQN的特性）
                # decay_counter += 1
                # if decay_counter >= 10:  # 每10张图片应用一次衰减
                #     dqn.apply_memory_decay()
                #     decay_counter = 0

            # 逐步降低探索率
            if EPISILO > 0.1:
                EPISILO = max(0.1, EPISILO - 0.1)

            print("RL Epoch: {}/{}, Reward: {}".format(i + 1, rl_epochs, round(ep_reward, 4)))

            # 记录训练指标
            metrics_recorder.record_epoch(i + 1, ep_reward, epoch_actions)

            # 检查是否需要更新最佳模型（只保存一个最佳模型）
            if ep_reward > best_rl_reward:
                best_rl_reward = ep_reward

                # 保存新的最佳模型
                dqn.save(save_dir=args.save_dir)

                # 保存完整模型信息
                best_rl_model_path = os.path.join(args.save_dir, 'best_models/best_rl_full.pth')
                torch.save({
                    'epoch': i + 1,
                    'reward': ep_reward,
                    'dqn': dqn.eval_net.state_dict(),
                    'hilbert_mapper': hilbert_mapper.state_dict()
                }, best_rl_model_path)
                print(f"新最佳RL模型保存成功，奖励: {ep_reward:.2f}")

    # 阶段2: 精细调整训练
    if refine_epochs > 0:
        print("===== 阶段2: 精细调整训练 =====")

        # 计算特征提取器模型复杂度
        if args.compute_flops:
            print("计算特征提取器的复杂度...")
            # 直接传入特征提取器的refine_head模块
            calculate_model_complexity("特征提取器精调头", feature_extractor.refine_head,
                                       input_shape=(1, feature_extractor.feat_channels,
                                                    feature_extractor.feat_size[0],
                                                    feature_extractor.feat_size[1]),
                                       device=device)

        # 自动加载第一阶段最佳模型
        best_rl_model_path = os.path.join(args.save_dir, 'best_models/best_rl_full.pth')
        if os.path.exists(best_rl_model_path) and not args.skip_rl:
            try:
                checkpoint = torch.load(best_rl_model_path, map_location=device, weights_only=False)
                if 'dqn' in checkpoint:
                    # 过滤掉统计键
                    filtered_dict = {k: v for k, v in checkpoint['dqn'].items()
                                     if 'total_ops' not in k and 'total_params' not in k}
                    dqn.eval_net.load_state_dict(filtered_dict, strict=False)
                    print(f"已加载第一阶段最佳Attention_DQN模型，奖励: {checkpoint['reward']:.2f}")
            except Exception as e:
                print(f"加载第一阶段最佳模型出错: {e}")

        # 冻结RL模块
        dqn.eval_net.eval()
        for param in dqn.eval_net.parameters():
            param.requires_grad = False

        # 解冻精细调整模块
        for param in feature_extractor.refine_head.parameters():
            param.requires_grad = True

        # 设置精细调整优化器和损失函数
        refine_optimizer = torch.optim.Adam(feature_extractor.refine_head.parameters(), lr=1e-4)
        refine_criterion = CombinedBBoxLoss(diou_weight=0.7, corner_weight=0.3).to(device)

        # 精细调整训练
        for i in range(refine_epochs):
            current_phase = PHASE_REFINE
            total_loss = 0
            total_diou_loss = 0
            total_corner_loss = 0
            refined_ious = []

            for index, image_name in enumerate(single_plane_image_names):
                image_path = get_image_path(image_name, path_voc)
                if image_path is None:
                    print(f"警告：找不到图像文件: {image_name}.jpg")
                    continue
                image_original = Image.open(image_path)
                width, height = image_original.size
                bbx_gt = single_plane_image_gts[index]

                # 使用固定的RL策略生成边界框
                bbx = [0, width, 0, height]
                history_action = np.zeros(his_actions * NUM_ACTIONS)

                # 使用RL策略得到最终边界框
                with torch.no_grad():
                    for step in range(50):
                        iou = cal_iou(bbx, bbx_gt)
                        if iou >= 0.9:
                            action = 8
                        else:
                            features = feature_extractor.extract_features(image_original, bbx)
                            state = feature_extractor.process_for_rl(features, bbx, (width, height), history_action,
                                                                     hilbert_mapper)
                            action = dqn.choose_action(state, 0)  # 低探索率

                        new_bbx = update_bbx(bbx, action)
                        history_action = update_history_actions(history_action, action)

                        bbx = new_bbx
                        if action == 8 or step == 49:
                            break

                final_bbox = bbx

                # 提取特征用于精细调整
                features = feature_extractor.extract_features(image_original, final_bbox)

                # 训练精细调整模块
                refine_optimizer.zero_grad()

                # 计算目标偏移量
                target_offsets = feature_extractor.calculate_target_offsets(final_bbox, bbx_gt)
                target_offsets_np = np.array(target_offsets, dtype=np.float32)
                target_tensor = torch.tensor(target_offsets_np, device=device).float().unsqueeze(0)

                # 检查refine_head是否可用
                if not hasattr(feature_extractor, 'refine_head') or feature_extractor.refine_head is None or len(
                        list(feature_extractor.refine_head.parameters())) == 0:
                    print(f"警告: refine_head不可用，跳过此训练步骤")
                    continue

                # 前向传播
                predicted_offsets = feature_extractor.refine_head(features)

                # 计算损失 - 将当前边界框作为锚点传递给损失函数
                final_bbox_np = np.array([final_bbox], dtype=np.float32)
                final_bbox_tensor = torch.tensor(final_bbox_np, device=device).float()
                loss = refine_criterion(predicted_offsets, target_tensor, final_bbox_tensor)

                # 分别记录DIoU损失和角点损失（用于监控训练）
                with torch.no_grad():
                    diou_loss = refine_criterion.diou_loss(predicted_offsets, target_tensor, final_bbox_tensor)
                    corner_loss = refine_criterion.corner_loss(predicted_offsets, target_tensor)
                    total_diou_loss += diou_loss.item()
                    total_corner_loss += corner_loss.item()

                # 反向传播
                loss.backward()
                refine_optimizer.step()

                total_loss += loss.item()

                # 计算精细调整后的IOU
                with torch.no_grad():
                    refined_bbox = feature_extractor.refine_bbox(features, final_bbox)
                    refined_iou = cal_iou(refined_bbox, bbx_gt)
                    refined_ious.append(refined_iou)

                # 最后一轮可视化
                if i == refine_epochs - 1:
                    # 可视化结果
                    draw = ImageDraw.Draw(image_original)
                    draw.rectangle([bbx_gt[0], bbx_gt[2], bbx_gt[1], bbx_gt[3]], outline='green', width=2)  # 绿框为目标框
                    draw.rectangle([final_bbox[0], final_bbox[2], final_bbox[1], final_bbox[3]], outline='red',
                                   width=2)  # 红框为RL框
                    draw.rectangle([refined_bbox[0], refined_bbox[2], refined_bbox[1], refined_bbox[3]], outline='blue',
                                   width=2)  # 蓝框为精调框

                    try:
                        if os.name == 'nt':
                            font = ImageFont.truetype("arial.ttf", 30)
                        else:
                            font = ImageFont.truetype("DejaVuSans.ttf", 30)
                    except IOError:
                        font = ImageFont.load_default()

                    draw.text([final_bbox[0], final_bbox[2]], f"RL:{cal_iou(final_bbox, bbx_gt):.2f}", fill=(255, 0, 0),
                              font=font)
                    draw.text([refined_bbox[0], refined_bbox[2]], f"RF:{refined_iou:.2f}", fill=(0, 0, 255), font=font)

                    # 保存可视化结果
                    vis_path = os.path.join(args.save_dir, 'visualization', 'visualization_aeroplane_train', f'refine_{image_name}.jpg')
                    os.makedirs(os.path.dirname(vis_path), exist_ok=True)
                    image_original.save(vis_path)

            avg_loss = total_loss / len(single_plane_image_names)
            avg_diou_loss = total_diou_loss / len(single_plane_image_names)
            avg_corner_loss = total_corner_loss / len(single_plane_image_names)
            avg_iou = sum(refined_ious) / len(refined_ious)
            print(
                f"Refine Epoch: {i + 1}/{refine_epochs}, Loss: {avg_loss:.4f} (DIoU: {avg_diou_loss:.4f}, Corner: {avg_corner_loss:.4f}), Avg IOU: {avg_iou:.4f}")

            # 检查是否需要更新最佳模型（只保存一个最佳模型）
            if avg_iou > best_refine_iou:
                best_refine_iou = avg_iou

                # 保存新的最佳模型
                feature_extractor.save(prefix='best_refine', save_dir=args.save_dir, hilbert_mapper=hilbert_mapper)
                # 保存完整模型信息
                best_refine_model_path = os.path.join(args.save_dir, 'best_models/best_refine_full.pth')
                torch.save({
                    'epoch': i + 1,
                    'iou': avg_iou,
                    'loss': avg_loss,
                    'refine_head': feature_extractor.refine_head.state_dict(),
                    'hilbert_mapper': hilbert_mapper.state_dict()
                }, best_refine_model_path)
                print(f"新最佳精细调整模型保存成功，IoU: {avg_iou:.4f}")

    # 阶段3: 联合微调
    if joint_epochs > 0 and not args.skip_joint:
        print("===== 阶段3: 联合训练 =====")

        # 计算联合模型复杂度 (Attention_DQN + 特征提取器)
        if args.compute_flops:
            calculate_complete_model_flops(dqn, feature_extractor, hilbert_mapper, args)

        # 自动加载前两个阶段的最佳模型
        # 加载第一阶段最佳Attention_DQN模型
        best_rl_model_path = os.path.join(args.save_dir, 'best_models/best_rl_full.pth')
        if os.path.exists(best_rl_model_path):
            try:
                checkpoint = torch.load(best_rl_model_path, map_location=device, weights_only=False)
                if 'dqn' in checkpoint:
                    # 过滤掉统计键
                    filtered_dict = {k: v for k, v in checkpoint['dqn'].items()
                                     if 'total_ops' not in k and 'total_params' not in k}
                    dqn.eval_net.load_state_dict(filtered_dict, strict=False)
                    print(f"已加载第一阶段最佳Attention_DQN模型，奖励: {checkpoint['reward']:.2f}")
            except Exception as e:
                print(f"加载第一阶段最佳模型出错: {e}")

        # 加载第二阶段最佳精细调整模型
        best_refine_model_path = os.path.join(args.save_dir, 'best_models/best_refine_full.pth')
        if os.path.exists(best_refine_model_path):
            try:
                checkpoint = torch.load(best_refine_model_path, map_location=device, weights_only=False)
                if 'refine_head' in checkpoint:
                    # 过滤掉统计键
                    filtered_dict = {k: v for k, v in checkpoint['refine_head'].items()
                                     if 'total_ops' not in k and 'total_params' not in k}
                    feature_extractor.refine_head.load_state_dict(filtered_dict, strict=False)
                    print(f"已加载第二阶段最佳精细调整模型，IoU: {checkpoint['iou']:.4f}")
            except Exception as e:
                print(f"加载第二阶段最佳模型出错: {e}")

        # 创建边界框回归的损失函数对象
        refine_criterion = CombinedBBoxLoss(diou_weight=0.7, corner_weight=0.3).to(device)

        # 设置联合训练优化器
        joint_optimizer = torch.optim.Adam([
            {'params': dqn.eval_net.parameters(), 'lr': 1e-5},
            {'params': feature_extractor.refine_head.parameters(), 'lr': 1e-4}
        ])

        # 确保网络处于正确的模式
        dqn.eval_net.train()
        dqn.target_net.eval()  # 目标网络保持评估模式
        feature_extractor.refine_head.train()

        # 联合训练
        for i in range(joint_epochs):
            current_phase = PHASE_JOINT

            # 计算当前训练进度(0~1之间)
            training_progress = i / (joint_epochs - 1) if joint_epochs > 1 else 1.0

            ep_reward = 0
            total_refine_loss = 0
            total_diou_loss = 0
            total_corner_loss = 0
            refined_ious = []

            for index, image_name in enumerate(single_plane_image_names):
                image_path = get_image_path(image_name, path_voc)
                if image_path is None:
                    print(f"警告：找不到图像文件: {image_name}.jpg")
                    continue
                image_original = Image.open(image_path)
                width, height = image_original.size
                bbx_gt = single_plane_image_gts[index]

                # 初始边界框
                bbx = [0, width, 0, height]
                history_action = np.zeros(his_actions * NUM_ACTIONS)
                step = 0

                # RL阶段轨迹
                rl_trajectory = []

                # 记录最终边界框和特征
                final_bbox = None
                final_features = None  # 用于边界框回归的完整特征
                final_rl_features = None  # 用于RL的降维特征

                while step < 50:
                    iou = cal_iou(bbx, bbx_gt)
                    if iou >= 0.9:
                        action = 8
                    else:
                        features = feature_extractor.extract_features(image_original, bbx)
                        # 获取希尔伯特空间降维后的特征用于RL
                        state = feature_extractor.process_for_rl(features, bbx, (width, height), history_action,
                                                                 hilbert_mapper)
                        action = dqn.choose_action(state, 0)  # 低探索率

                    new_bbx = update_bbx(bbx, action)
                    reward = reward_func(bbx, new_bbx, bbx_gt, action, training_progress=training_progress)
                    ep_reward += reward

                    # 提取下一个特征
                    next_features = feature_extractor.extract_features(image_original, new_bbx)
                    # 获取希尔伯特空间降维后的特征用于RL
                    next_state = feature_extractor.process_for_rl(next_features, new_bbx, (width, height),
                                                                  history_action, hilbert_mapper)

                    # 保存轨迹用于RL训练
                    rl_trajectory.append((state, action, reward, next_state))

                    history_action = update_history_actions(history_action, action)
                    bbx = new_bbx

                    # 记录最终边界框和特征
                    final_bbox = bbx
                    final_features = next_features  # 完整特征，用于边界框回归
                    final_rl_features = next_state  # 降维特征，用于RL

                    if action == 8 or step == 49:
                        break

                    step += 1

                # RL训练 - 使用收集的轨迹更新Attention_DQN
                for state, action, reward, next_state in rl_trajectory:
                    dqn.store_transition(state, action, reward, next_state)
                    if dqn.memory_counter >= MEMORY_CAPACITY:
                        dqn.learn()

                # 精细调整训练
                joint_optimizer.zero_grad()

                # 计算目标偏移量
                target_offsets = feature_extractor.calculate_target_offsets(final_bbox, bbx_gt)
                target_offsets_np = np.array(target_offsets, dtype=np.float32)
                target_tensor = torch.tensor(target_offsets_np, device=device).float().unsqueeze(0)

                # 检查refine_head是否可用
                if not hasattr(feature_extractor, 'refine_head') or feature_extractor.refine_head is None or len(
                        list(feature_extractor.refine_head.parameters())) == 0:
                    print(f"警告: refine_head不可用，跳过此训练步骤")
                    continue

                # 前向传播 - 直接使用完整特征，而不是降维后的特征
                predicted_offsets = feature_extractor.refine_head(final_features)

                # 计算损失 - 将当前边界框作为锚点传递给损失函数
                final_bbox_np = np.array([final_bbox], dtype=np.float32)
                final_bbox_tensor = torch.tensor(final_bbox_np, device=device).float()
                loss = refine_criterion(predicted_offsets, target_tensor, final_bbox_tensor)

                # 分别记录DIoU损失和角点损失（用于监控训练）
                with torch.no_grad():
                    diou_loss = refine_criterion.diou_loss(predicted_offsets, target_tensor, final_bbox_tensor)
                    corner_loss = refine_criterion.corner_loss(predicted_offsets, target_tensor)
                    total_diou_loss += diou_loss.item()
                    total_corner_loss += corner_loss.item()

                # 反向传播
                loss.backward()
                joint_optimizer.step()

                total_refine_loss += loss.item()

                # 评估结果 - 使用完整特征进行边界框回归
                with torch.no_grad():
                    refined_bbox = feature_extractor.refine_bbox(final_features, final_bbox)
                    refined_iou = cal_iou(refined_bbox, bbx_gt)
                    refined_ious.append(refined_iou)

                # 最后一轮可视化
                if i == joint_epochs - 1:
                    # 可视化结果
                    draw = ImageDraw.Draw(image_original)
                    draw.rectangle([bbx_gt[0], bbx_gt[2], bbx_gt[1], bbx_gt[3]], outline='green', width=2)  # 绿框为目标框
                    draw.rectangle([final_bbox[0], final_bbox[2], final_bbox[1], final_bbox[3]], outline='red',
                                   width=2)  # 红框为RL框
                    draw.rectangle([refined_bbox[0], refined_bbox[2], refined_bbox[1], refined_bbox[3]],
                                   outline='purple', width=2)  # 紫框为联合训练框

                    try:
                        if os.name == 'nt':
                            font = ImageFont.truetype("arial.ttf", 30)
                        else:
                            font = ImageFont.truetype("DejaVuSans.ttf", 30)
                    except IOError:
                        font = ImageFont.load_default()

                    draw.text([final_bbox[0], final_bbox[2]], f"RL:{cal_iou(final_bbox, bbx_gt):.2f}", fill=(255, 0, 0),
                              font=font)
                    draw.text([refined_bbox[0], refined_bbox[2]], f"JT:{refined_iou:.2f}", fill=(128, 0, 128),
                              font=font)

                    # 保存可视化结果
                    vis_path = os.path.join(args.save_dir, 'visualization', 'visualization_aeroplane_train', f'joint_{image_name}.jpg')
                    os.makedirs(os.path.dirname(vis_path), exist_ok=True)
                    image_original.save(vis_path)

            avg_refine_loss = total_refine_loss / len(single_plane_image_names)
            avg_diou_loss = total_diou_loss / len(single_plane_image_names)
            avg_corner_loss = total_corner_loss / len(single_plane_image_names)
            avg_iou = sum(refined_ious) / len(refined_ious)
            print(
                f"Joint Epoch: {i + 1}/{joint_epochs}, Reward: {ep_reward:.4f}, Refine Loss: {avg_refine_loss:.4f} (DIoU: {avg_diou_loss:.4f}, Corner: {avg_corner_loss:.4f}), Avg IOU: {avg_iou:.4f}")

            # 检查是否需要更新最佳模型（根据IoU指标）- 只保存一个最佳模型
            if avg_iou > best_joint_iou:
                best_joint_iou = avg_iou

                # 删除旧的最佳模型文件（如果存在）
                old_best_path = os.path.join(args.save_dir, 'best_models/best_joint_full.pth')
                if os.path.exists(old_best_path):
                    try:
                        os.remove(old_best_path)
                    except Exception as e:
                        print(f"删除旧模型文件时出错: {e}")

                # 保存新的最佳模型
                feature_extractor.save(prefix='best_joint', save_dir=args.save_dir, hilbert_mapper=hilbert_mapper)
                dqn.save(prefix='best_joint', save_dir=args.save_dir, hilbert_mapper=hilbert_mapper)
                # 单独保存希尔伯特映射器
                hilbert_mapper.save(prefix='best_joint', save_dir=args.save_dir)

                # 保存完整模型信息 - 使用统一的文件名
                best_joint_model_path = os.path.join(args.save_dir, 'best_models/best_joint_full.pth')
                torch.save({
                    'epoch': i + 1,
                    'iou': avg_iou,
                    'reward': ep_reward,
                    'loss': avg_refine_loss,
                    'dqn': dqn.eval_net.state_dict(),
                    'refine_head': feature_extractor.refine_head.state_dict(),
                    'hilbert_mapper': hilbert_mapper.state_dict()
                }, best_joint_model_path)
                print(f"新最佳联合微调模型保存成功，IoU: {avg_iou:.4f}, 奖励: {ep_reward:.2f}")

        # 最终模型（只保存最佳模型，不需要保存最终模型）
        print("训练完成。最佳模型已保存。")

    # 保存训练损失曲线
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.plot(np.arange(len(dqn.train_loss)), dqn.train_loss)
    plt.xlabel('Learn Step')
    plt.ylabel('Attention_DQN Loss')
    plt.title('Attention_DQN Training Loss')

    # 添加保存图表的代码
    plt.tight_layout()
    loss_path = os.path.join(args.save_dir, 'visualization', 'loss', 'training_losses.png')
    os.makedirs(os.path.dirname(loss_path), exist_ok=True)
    plt.savefig(loss_path)
    plt.close()

    # 训练完成后进行评估
    print("\n===== 训练完成，进行最终评估 =====")

    # 加载评估集
    if not args.eval_only:  # 如果不是仅评估模式，则加载评估数据集
        eval_image_names = load_images_names_in_data_set(args.eval_set,
                                                         args.test_voc_path if args.test_voc_path else path_voc)
        print(f"评估数据集: {args.eval_set}")
        print(f"评估集图像数量: {len(eval_image_names)}")

    # 进行评估
    avg_iou, above_threshold, avg_steps = evaluate_model(
        dqn,
        feature_extractor,
        hilbert_mapper,
        eval_image_names,
        device,
        args.save_dir,
        phase_name=f"final_{args.eval_set}",
        test_voc_path=args.test_voc_path,
        use_bbox_regression=not args.no_bbox_regression
    )

    # 打印最终结果
    print("\n===== 最终评估结果 =====")
    print(f"数据集: {args.eval_set}")
    print(f"平均IoU: {avg_iou:.4f}")
    print(f"平均步数: {avg_steps:.2f}")
    print(f"IoU>0.5比例: {above_threshold:.4f}")
    print(f"边界框回归: {'禁用' if args.no_bbox_regression else '启用'}")

    # 记录最终评估结果并完成训练记录
    if not args.eval_only:  # 只有在训练模式下才记录最终指标
        # 记录最终评估结果（AP50使用IoU>0.5的比例）
        final_epoch = rl_epochs if rl_epochs > 0 else 1
        metrics_recorder.record_evaluation(final_epoch, above_threshold, avg_iou)

        # 完成训练记录
        metrics_recorder.finalize_training()
        print(f"📊 Attention_DQN训练指标记录完成，数据已保存到 dqn_metrics/ 目录")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Hierarchical Object Detection with Deep Reinforcement Learning')
    parser.add_argument('--gpu-devices', default='0', type=str, help='gpu device ids for CUDA_VISIBLE_DEVICES')
    parser.add_argument('--use_gpu', default=True, action='store_true')
    parser.add_argument('--EPISILO', type=float, default=0.9)
    parser.add_argument('--rl_epochs', type=int, default=50)
    parser.add_argument('--refine_epochs', type=int, default=100)
    parser.add_argument('--joint_epochs', type=int, default=100)
    parser.add_argument('--save_dir', type=str, default='aeroplane',
                        help='保存模型和可视化结果的目录')
    parser.add_argument('--load_dqn', type=str,
                        default=r"E:\deeplearn\消融实验\无DQN\Attention_DQN\aeroplane\best_models\best_rl_full.pth",
                        help='Attention_DQN模型权重文件路径')
    parser.add_argument('--load_refine', type=str,
                        default=None,
                        help='边界框回归模型权重文件路径')
    parser.add_argument('--load_model', type=str,
                        default=None,
                        help='完整模型权重文件路径(包含Attention_DQN和边界框回归)')
    parser.add_argument('--skip_rl', action='store_true', default=False, help='跳过RL阶段训练')
    parser.add_argument('--skip_refine', action='store_true', default=True, help='跳过边界框回归阶段训练')
    parser.add_argument('--skip_joint', action='store_true', default=True, help='跳过联合优化阶段训练')
    # 添加FLOPs计算相关参数
    parser.add_argument('--compute_flops', action='store_true', default=True, help='计算模型FLOPs')
    parser.add_argument('--flops_detail', action='store_true', default=True, help='显示详细的FLOPs计算信息')
    parser.add_argument('--flops_backbone', action='store_true', default=True, help='计算backbone的FLOPs')
    # 添加仅评估模式参数
    parser.add_argument('--eval_only', action='store_true', default=True, help='仅进行评估，不进行训练')
    parser.add_argument('--eval_set', type=str, default=
    'aeroplane_test', help='评估数据集，默认为验证集')
    parser.add_argument('--test_voc_path', type=str, default=r'D:\VOCdevkit\VOCdevkit',
                        help='VOC2007测试集路径')
    parser.add_argument('--no_bbox_regression', action='store_true', default=False,
                        help='测试时不使用边界框回归')

    main(parser.parse_args())
