import torch
from torch import nn
from torch.nn import init
import torch.nn.functional as F
import torchvision
import numpy as np



def weights_init_kaiming(m):  # 权重初始化
    classname = m.__class__.__name__  # 得到网络层的名字
    # print(classname)
    if classname.find('Conv') != -1:  # 使用了find函数，如果不存在返回值为-1，所以让其不等于-1
        init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
        # 用正态分布的值填充输入张量, 张量中的值采样自均值为0，标准差为sqrt(2/((1 + a^2) * fan_in))的正态分布
        # 参数a：该层后面一层的激活函数中负的斜率(默认为ReLU，a=0)
        # fan_in 保持weights的方差在 前向传播中不变
    elif classname.find('Linear') != -1:
        init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
    elif classname.find('BatchNorm1d') != -1:
        # BatchNorm1d层对小批量(mini-batch)的2d或3d输入进行批标准化(Batch Normalization)操作
        init.normal_(m.weight.data, 1.0, 0.02)  # 正态分布,期望为1，标准差为0.02
        init.constant_(m.bias.data, 0.0)  # 将tensor(m.bias.data)填充为常量值(0.0)


def weights_init_kaiming_r(m):  # 权重初始化
    classname = m.__class__.__name__  # 得到网络层的名字
    # print(classname)
    if classname.find('Conv') != -1:  # 使用了find函数，如果不存在返回值为-1，所以让其不等于-1
        init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
        # 用正态分布的值填充输入张量, 张量中的值采样自均值为0，标准差为sqrt(2/((1 + a^2) * fan_in))的正态分布
        # 参数a：该层后面一层的激活函数中负的斜率(默认为ReLU，a=0)
        # fan_in 保持weights的方差在 前向传播中不变
    elif classname.find('Linear') != -1:
        init.normal_(m.weight.data, 0, 0.01)
        init.constant_(m.bias.data, 0.0)  # 将tensor(m.bias.data)填充为常量值(0.0)
    elif classname.find('BatchNorm1d') != -1:
        # BatchNorm1d层对小批量(mini-batch)的2d或3d输入进行批标准化(Batch Normalization)操作
        init.normal_(m.weight.data, 1.0, 0.02)  # 正态分布,期望为1，标准差为0.02
        init.constant_(m.bias.data, 0.0)  # 将tensor(m.bias.data)填充为常量值(0.0)


def weights_init_classifier(m):
    classname = m.__class__.__name__
    if classname.find('Linear') != -1:
        init.normal_(m.weight.data, std=0.02)  # 正态分布,期望为1，标准差为0.02
        init.constant_(m.bias.data, 0.0)  # 将tensor(m.bias.data)填充为常量值(0.0)


class Net(nn.Module):
    """
    DQN的神经网络架构
    """
    def __init__(self, state_dim=2048):  # 修改为降维后的维度
        super(Net, self).__init__()
        self.fc1 = nn.Linear(state_dim, 512)
        self.fc2 = nn.Linear(512, 128)
        self.fc3 = nn.Linear(128, 9)  # 9个动作

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        action_value = self.fc3(x)
        action_value = F.softmax(action_value, dim=1)
        return action_value

