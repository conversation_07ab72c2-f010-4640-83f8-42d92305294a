{"method_name": "DoubleDQN", "start_time": "2025-08-02T23:25:24.889107", "epochs": [1, 2, 3, 4, 5, 6, 7, 8], "rewards": [-3247.4913532145124, -3206.140140356986, -2830.718264292586, -2645.472631630769, -2611.99121264669, -2376.4733946712267, -2251.184840027501, -2264.9411574175983], "reward_std_window": [0.0, 0.0, 187.48372239922486, 253.40701652441615, 270.7971758996612, 316.8624214478483, 354.4536236689359, 366.6885591555516], "policy_entropy": [2.175460889972062, 2.121038360191858, 2.0458917761002793, 1.9615822743903222, 1.8314105281579736, 1.6892455605948058, 1.536363230271897, 1.333877364560405], "ap50_values": [], "map_values": [], "convergence_epoch": null, "convergence_threshold": -1000, "final_metrics": {}, "training_completed": false}