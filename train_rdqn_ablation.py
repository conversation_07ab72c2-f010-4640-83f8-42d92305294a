#!/usr/bin/env python3
"""
RDQN消融实验批量训练脚本
方便一次性训练所有RDQN消融实验变体
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def train_rdqn_ablation_variant(variant_name, epochs=50):
    """训练单个RDQN消融实验变体"""
    print(f"\n{'='*80}")
    print(f"🚀 开始训练: {variant_name}")
    print(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔄 训练轮次: {epochs}")
    print(f"{'='*80}")
    
    start_time = time.time()
    
    try:
        # 构建训练命令
        cmd = [
            'python', 'train.py',
            '--epochs', str(epochs),
            '--model_class', variant_name,
            '--gpu-devices', '0'
        ]
        
        print(f"📋 执行命令: {' '.join(cmd)}")
        print(f"📁 工作目录: RDQN_Ablation")
        
        # 执行训练
        process = subprocess.Popen(
            cmd,
            cwd='RDQN_Ablation',
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 收集输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"[{variant_name}] {output.strip()}")
                output_lines.append(output)
        
        return_code = process.poll()
        training_time = time.time() - start_time
        
        if return_code == 0:
            print(f"✅ {variant_name} 训练成功完成")
            print(f"⏱️  训练耗时: {training_time/60:.1f} 分钟")
            return True
        else:
            print(f"❌ {variant_name} 训练失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ {variant_name} 训练异常: {e}")
        return False

def train_all_rdqn_ablation_variants(epochs=50):
    """训练所有RDQN消融实验变体"""
    
    # RDQN消融实验变体列表
    ablation_variants = [
        'RDQN_NoImportance',      # 无重要性采样
        'RDQN_NoMetaplasticity',  # 无元可塑性
        'RDQN_UniformSync'        # 均匀同步
    ]
    
    print(f"🎯 RDQN消融实验批量训练")
    print(f"📊 将训练 {len(ablation_variants)} 个消融实验变体")
    print(f"⏱️  预计总时间: {len(ablation_variants) * epochs * 2} 分钟")
    
    experiment_start_time = time.time()
    results = {}
    
    # 训练所有变体
    for i, variant in enumerate(ablation_variants, 1):
        print(f"\n🔄 进度: {i}/{len(ablation_variants)} - 当前训练: {variant}")
        
        success = train_rdqn_ablation_variant(variant, epochs)
        results[variant] = success
        
        if success:
            print(f"✅ {variant} 完成")
        else:
            print(f"❌ {variant} 失败")
    
    # 总结报告
    total_time = time.time() - experiment_start_time
    success_count = sum(results.values())
    
    print(f"\n{'='*80}")
    print(f"🎉 RDQN消融实验批量训练完成!")
    print(f"{'='*80}")
    print(f"⏱️  总耗时: {total_time/3600:.2f} 小时")
    print(f"📊 成功率: {success_count}/{len(ablation_variants)} ({success_count/len(ablation_variants)*100:.1f}%)")
    
    print(f"\n📋 训练结果:")
    for variant, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {variant:<25}: {status}")
    
    if success_count == len(ablation_variants):
        print(f"\n🎊 所有RDQN消融实验变体训练成功!")
        print(f"📈 现在可以运行分析脚本生成对比图表:")
        print(f"   python dqn_analysis_plotter.py")
    else:
        print(f"\n⚠️  部分变体训练失败，请检查错误信息")
    
    print(f"{'='*80}")

def show_usage():
    """显示使用说明"""
    print("\n" + "="*80)
    print("🎯 RDQN消融实验训练脚本使用说明")
    print("="*80)
    
    print("\n📋 支持的RDQN消融实验变体:")
    variants = [
        ("RDQN_NoImportance", "移除重要性采样机制"),
        ("RDQN_NoMetaplasticity", "移除元可塑性机制"),
        ("RDQN_UniformSync", "使用均匀同步策略")
    ]
    
    for i, (variant, description) in enumerate(variants, 1):
        print(f"  {i}. {variant:<20} - {description}")
    
    print("\n🚀 使用方法:")
    print("1. 训练所有消融实验变体:")
    print("   python train_rdqn_ablation.py")
    print("   python train_rdqn_ablation.py --epochs 50")
    
    print("\n2. 训练单个变体:")
    print("   cd RDQN_Ablation")
    print("   python train.py --epochs 50 --model_class RDQN_NoImportance")
    print("   python train.py --epochs 50 --model_class RDQN_NoMetaplasticity")
    print("   python train.py --epochs 50 --model_class RDQN_UniformSync")
    
    print("\n3. 生成对比分析:")
    print("   python dqn_analysis_plotter.py")
    
    print("\n📊 输出数据:")
    print("   - dqn_metrics/: 各变体的训练指标数据")
    print("   - dqn_analysis_plots/: 对比分析图表")
    
    print("\n" + "="*80)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RDQN消融实验批量训练脚本')
    parser.add_argument('--epochs', type=int, default=50,
                       help='训练轮次 (默认: 50)')
    parser.add_argument('--variant', type=str, default=None,
                       help='训练单个变体 (可选: RDQN_NoImportance, RDQN_NoMetaplasticity, RDQN_UniformSync)')
    
    args = parser.parse_args()
    
    if args.variant:
        # 训练单个变体
        if args.variant in ['RDQN_NoImportance', 'RDQN_NoMetaplasticity', 'RDQN_UniformSync']:
            success = train_rdqn_ablation_variant(args.variant, args.epochs)
            if success:
                print(f"\n🎉 {args.variant} 训练完成!")
            else:
                print(f"\n❌ {args.variant} 训练失败!")
        else:
            print(f"❌ 不支持的变体: {args.variant}")
            show_usage()
    else:
        # 训练所有变体
        train_all_rdqn_ablation_variants(args.epochs)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        show_usage()
    else:
        main()
