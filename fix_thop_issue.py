#!/usr/bin/env python3
"""
修复所有DQN变体中的thop库冲突问题
"""

import os
import re

def fix_calculate_model_complexity(file_path):
    """修复单个文件中的calculate_model_complexity函数"""
    
    if not os.path.exists(file_path):
        print(f"⚠️ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找需要替换的函数
        old_pattern = r'''def calculate_model_complexity\(model_name, model, input_shape=None, device='cuda'\):
    """
    计算模型复杂度
    """
    try:
        if input_shape is None:
            input_shape = \(1, NUM_STATES\)
        
        input_tensor = torch\.randn\(input_shape\)\.to\(device if torch\.cuda\.is_available\(\) else 'cpu'\)
        # 确保模型在正确的设备上
        model_device = next\(model\.parameters\(\)\)\.device
        input_tensor = input_tensor\.to\(model_device\)

        macs, params = profile\(model, inputs=\(input_tensor,\), verbose=False\)
        flops = macs \* 2
        return f"\{flops / 1e9:.2f\} GFLOPs", f"\{macs / 1e9:.2f\} GMACs", f"\{params / 1e6:.2f\} M"
    except Exception as e:
        print\(f"计算\{model_name\}复杂度时出错: \{e\}"\)
        return None, None, None'''
        
        new_function = '''def calculate_model_complexity(model_name, model, input_shape=None, device='cuda'):
    """
    计算模型复杂度
    """
    try:
        if input_shape is None:
            input_shape = (1, NUM_STATES)
        
        input_tensor = torch.randn(input_shape).to(device if torch.cuda.is_available() else 'cpu')
        # 确保模型在正确的设备上
        model_device = next(model.parameters()).device
        input_tensor = input_tensor.to(model_device)

        macs, params = profile(model, inputs=(input_tensor,), verbose=False)
        flops = macs * 2
        
        # 清理thop添加的属性，避免状态字典冲突
        def clean_thop_attributes(module):
            if hasattr(module, 'total_ops'):
                delattr(module, 'total_ops')
            if hasattr(module, 'total_params'):
                delattr(module, 'total_params')
            for child in module.children():
                clean_thop_attributes(child)
        
        clean_thop_attributes(model)
        
        return f"{flops / 1e9:.2f} GFLOPs", f"{macs / 1e9:.2f} GMACs", f"{params / 1e6:.2f} M"
    except Exception as e:
        print(f"计算{model_name}复杂度时出错: {e}")
        return None, None, None'''
        
        # 使用更简单的字符串替换
        old_simple = '''        macs, params = profile(model, inputs=(input_tensor,), verbose=False)
        flops = macs * 2
        return f"{flops / 1e9:.2f} GFLOPs", f"{macs / 1e9:.2f} GMACs", f"{params / 1e6:.2f} M"'''
        
        new_simple = '''        macs, params = profile(model, inputs=(input_tensor,), verbose=False)
        flops = macs * 2
        
        # 清理thop添加的属性，避免状态字典冲突
        def clean_thop_attributes(module):
            if hasattr(module, 'total_ops'):
                delattr(module, 'total_ops')
            if hasattr(module, 'total_params'):
                delattr(module, 'total_params')
            for child in module.children():
                clean_thop_attributes(child)
        
        clean_thop_attributes(model)
        
        return f"{flops / 1e9:.2f} GFLOPs", f"{macs / 1e9:.2f} GMACs", f"{params / 1e6:.2f} M"'''
        
        if old_simple in content:
            new_content = content.replace(old_simple, new_simple)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 修复成功: {file_path}")
            return True
        else:
            print(f"⚠️ 未找到需要修复的代码: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {file_path}, 错误: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复所有DQN变体中的thop库冲突问题...")
    
    # 需要修复的DQN变体目录
    dqn_variants = [
        "DoubleDQN",
        "DuelingDQN", 
        "EWC_DQN",
        "LSTM_DQN",
        "PER_DQN",
        "RDQN",
        "Attention_DQN",
        "RDQN_Ablation"
    ]
    
    success_count = 0
    total_count = len(dqn_variants)
    
    for variant in dqn_variants:
        train_file = os.path.join(variant, "train.py")
        print(f"\n🔍 处理: {variant}")
        
        if fix_calculate_model_complexity(train_file):
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"🎉 修复完成!")
    print(f"📊 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print(f"✅ 所有DQN变体都已成功修复!")
        print(f"🚀 现在可以正常训练了:")
        print(f"   cd DQN && python train.py --epochs 50")
    else:
        print(f"⚠️ 部分变体修复失败，请手动检查")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
