import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import timm
import torchvision.transforms as T
from PIL import Image
from hilbert_mapping import ResidualEnhancedHilbertMapping
import pickle
import math
import torch.serialization




# 从net.py导入的初始化函数
def weights_init_kaiming(m):
    """
    对神经网络层进行Kaiming初始化
    """
    if isinstance(m, nn.Conv2d):
        nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
        if m.bias is not None:
            nn.init.constant_(m.bias, 0)
    elif isinstance(m, nn.Linear):
        nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
        nn.init.constant_(m.bias, 0)
    elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
        nn.init.constant_(m.weight, 1)
        nn.init.constant_(m.bias, 0)


# 基础网络模型定义（从net.py移植）
class Net(nn.Module):
    """
    DQN的神经网络架构
    """

    def __init__(self, state_dim=2048):  # 修改为降维后的维度
        super(Net, self).__init__()
        self.fc1 = nn.Linear(state_dim, 1024)
        self.fc2 = nn.Linear(1024, 512)
        self.fc3 = nn.Linear(512, 9)  # 9个动作

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        action_value = self.fc3(x)
        action_value = F.softmax(action_value, dim=1)
        return action_value


# 新增：忆阻器调制层
class MemristorLayer(nn.Module):
    def __init__(self, input_dim, output_dim, decay_rate=0.01, importance_rate=0.01, activation_strength=0.1,
                 is_target=False):
        super(MemristorLayer, self).__init__()
        # 基本线性层参数
        self.weight = nn.Parameter(torch.Tensor(output_dim, input_dim))
        self.bias = nn.Parameter(torch.Tensor(output_dim))

        # 忆阻器特性参数 - 确保在与权重相同的设备上初始化
        self.register_buffer('memory_strength', torch.zeros_like(self.weight.data))
        self.register_buffer('importance', torch.ones_like(self.weight.data))

        # 配置
        self.decay_rate = decay_rate  # 目标网络较小，评估网络较大
        self.importance_rate = importance_rate  # 目标网络较小，评估网络较大
        self.activation_strength = activation_strength  # 控制激活对记忆的影响
        self.is_target = is_target

        # 初始化 - 使用Kaiming初始化提高稳定性
        nn.init.kaiming_uniform_(self.weight, a=math.sqrt(5))
        fan_in, _ = nn.init._calculate_fan_in_and_fan_out(self.weight)
        bound = 1 / math.sqrt(fan_in)
        nn.init.uniform_(self.bias, -bound, bound)

        # 用于跟踪参数变化的历史 - 也使用register_buffer确保设备一致
        self.register_buffer('param_change_history', torch.zeros_like(self.weight.data))
        self.momentum = 0.9

    def forward(self, input):
        # 计算有效权重（应用记忆强度调制）
        effective_weight = self.weight * torch.sigmoid(self.memory_strength * self.importance)

        # 应用线性变换 - 确保梯度传播
        output = F.linear(input, effective_weight, self.bias)

        # 在训练模式下更新记忆强度
        if self.training and not self.is_target:
            with torch.no_grad():
                # 计算激活强度（简化模型，实际可以使用更复杂的策略）
                batch_activation = torch.abs(input).mean(0, keepdim=True)  # [1, input_dim]
                weight_activation = torch.abs(self.weight)  # [output_dim, input_dim]

                # 更新记忆强度 - 确保设备一致性
                self.memory_strength += self.activation_strength * torch.matmul(
                    torch.ones(weight_activation.size(0), 1, device=input.device),
                    batch_activation
                ) * weight_activation

        return output

    def apply_decay(self):
        """应用记忆衰减"""
        with torch.no_grad():
            self.memory_strength *= (1 - self.decay_rate)

    def update_importance(self, td_error):
        """基于TD误差更新重要性"""
        with torch.no_grad():
            # 简化模型，实际中可能需要更精细的重要性计算
            importance_update = self.importance_rate * abs(td_error)
            self.importance += importance_update

    def update_metaplasticity(self, prev_weight):
        """更新萌发可塑性（记录参数变化）"""
        with torch.no_grad():
            current_change = torch.abs(self.weight.data - prev_weight)
            self.param_change_history = (
                    self.momentum * self.param_change_history +
                    (1 - self.momentum) * current_change
            )


# 特征提取器模型
class MobileFeatureExtractor(nn.Module):
    """
    移动网络特征提取器，用于提供RL状态表示和区域特征
    """

    def __init__(self, device='cuda'):
        super().__init__()
        # 使用RepVGG-A0作为特征提取器
        self.backbone = timm.create_model(
            'repvgg_a0',
            pretrained=True,
            features_only=True,
            out_indices=[3]  # 使用最后一个特征层
        ).to(device)

        # 设置为评估模式
        self.backbone.eval()

        # 冻结backbone参数
        for param in self.backbone.parameters():
            param.requires_grad = False

        # 获取特征维度
        self.device = device
        dummy = torch.zeros(1, 3, 128, 128).to(device)
        with torch.no_grad():
            test_output = self.backbone(dummy)[0]
            self.feat_channels = test_output.shape[1]
            self.feat_size = test_output.shape[2:]
            print(f"特征维度: {self.feat_channels}, 特征图大小: {self.feat_size}")

        # 计算展平后的特征维度
        self.flat_feat_dim = self.feat_channels * self.feat_size[0] * self.feat_size[1]
        print(f"展平后的特征维度: {self.flat_feat_dim}")

        # 添加希尔伯特映射层，用于降维
        self.original_state_dim = self.flat_feat_dim + 8 + 90  # 原始状态维度：特征+几何特征(8)+历史动作(90)
        self.hilbert_mapper = ResidualEnhancedHilbertMapping(
            input_dim=self.original_state_dim,
            output_dim=4096,  # 降维到4096
            gamma=0.01,  # 控制映射的平滑度
            residual_ratio=0.5  # 控制残差比例
        ).to(device)
        print(f"状态维度: {self.original_state_dim}")

        # RoIAlign精细调整组件 - 直接使用完整维度特征，不经过希尔伯特降维
        self.refine_head = nn.Sequential(
            nn.Conv2d(self.feat_channels, 256, 1),
            nn.ReLU(),
            nn.Conv2d(256, 64, 1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(64, 16),
            nn.ReLU(),
            nn.Linear(16, 4)
        ).to(device)

        # 预处理变换
        self.preprocess = T.Compose([
            T.Resize((128, 128)),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def extract_features(self, image, bbox):
        """提取特定边界框内的特征"""
        # 裁剪和预处理
        crop = image.crop((bbox[0], bbox[2], bbox[1], bbox[3]))
        input_tensor = self.preprocess(crop).unsqueeze(0).to(self.device)

        # 特征提取
        with torch.no_grad():
            features = self.backbone(input_tensor)[0]

        return features

    def get_full_features(self, features, bbox, image_size, history_actions):
        """获取未经希尔伯特空间降维的完整特征，用于边界框回归

        参数:
        - features: RepVGG提取的特征 [1, channels, height, width]
        - bbox: 边界框 [x1, x2, y1, y2]
        - image_size: 图像尺寸 (width, height)
        - history_actions: 历史动作

        返回:
        - full_features: 完整特征（不经过希尔伯特空间降维）
        """
        # 直接使用RepVGG提取的特征，不进行希尔伯特降维
        # 边界框回归模块直接使用这些特征
        return features

    def process_for_rl(self, features, bbox, image_size, history_actions):
        """处理特征用于RL决策，使用希尔伯特映射降维"""
        # 展平特征
        with torch.no_grad():
            flat_feats = features.reshape(1, -1)

            # 几何特征 - 8维
            width, height = image_size
            geometric_feats = torch.tensor([
                bbox[0] / width, bbox[1] / width, bbox[2] / height, bbox[3] / height,
                (bbox[1] - bbox[0]) / width, (bbox[3] - bbox[2]) / height,
                (bbox[0] + bbox[1]) / (2 * width), (bbox[2] + bbox[3]) / (2 * height)
            ], device=self.device).float()

            # 确保history_actions是numpy数组
            history_actions_np = np.array(history_actions, dtype=np.float32)
            history_actions_tensor = torch.tensor(history_actions_np, device=self.device)

            # 组合状态表示 - 特征向量 + 几何特征(8) + 历史动作(90)
            combined_state = torch.cat([
                flat_feats.reshape(-1),
                geometric_feats,
                history_actions_tensor
            ])

            # 使用希尔伯特映射降维
            mapped_state = self.hilbert_mapper(combined_state.unsqueeze(0)).squeeze(0)

            # 转换为numpy数组用于DQN
            rl_state = mapped_state.detach().cpu().numpy()

        return rl_state

    def refine_bbox(self, features, bbox):
        """基于特征精细调整边界框 - 直接使用完整特征，不经过希尔伯特降维"""
        # 在推理模式下运行，不需要梯度
        with torch.no_grad():
            # 检查refine_head是否可用
            if not hasattr(self, 'refine_head') or self.refine_head is None or len(
                    list(self.refine_head.parameters())) == 0:
                # 如果refine_head不可用，直接返回原始边界框
                return bbox.copy() if isinstance(bbox, list) else bbox.copy()

            # 使用完整特征(不经过希尔伯特降维)预测调整量
            # features已经是完整特征，不需要额外处理
            refinement = self.refine_head(features)  # [1, 4]

            # 计算原始边界框的宽度和高度
            width = bbox[1] - bbox[0]
            height = bbox[3] - bbox[2]

            # 计算调整后的边界框坐标
            refined_x1 = bbox[0] + refinement[0, 0].item() * width
            refined_x2 = bbox[1] + refinement[0, 1].item() * width
            refined_y1 = bbox[2] + refinement[0, 2].item() * height
            refined_y2 = bbox[3] + refinement[0, 3].item() * height

            # 确保坐标不会为负
            refined_x1 = max(0, refined_x1)
            refined_y1 = max(0, refined_y1)

            # 确保x2>x1, y2>y1，并至少有最小尺寸
            min_size = 5  # 最小尺寸（像素）
            if refined_x2 <= refined_x1 + min_size:
                refined_x2 = refined_x1 + min_size

            if refined_y2 <= refined_y1 + min_size:
                refined_y2 = refined_y1 + min_size

            refined_bbox = [refined_x1, refined_x2, refined_y1, refined_y2]

        return refined_bbox

    def calculate_target_offsets(self, current_bbox, gt_bbox):
        """
        计算当前边界框到真实边界框的目标偏移量

        参数:
        - current_bbox: [x1, x2, y1, y2] 当前预测的边界框
        - gt_bbox: [x1, x2, y1, y2] 真实边界框

        返回:
        - offsets: [dx1, dx2, dy1, dy2] 相对于当前边界框尺寸的偏移量
        """
        # 计算当前边界框的宽度和高度
        width = current_bbox[1] - current_bbox[0]
        height = current_bbox[3] - current_bbox[2]

        # 计算相对偏移量
        dx1 = (gt_bbox[0] - current_bbox[0]) / width
        dx2 = (gt_bbox[1] - current_bbox[1]) / width
        dy1 = (gt_bbox[2] - current_bbox[2]) / height
        dy2 = (gt_bbox[3] - current_bbox[3]) / height

        # 返回归一化的偏移量
        return [dx1, dx2, dy1, dy2]

    def load_refine_head(self, checkpoint_path):
        """加载预训练的refine_head权重

        参数:
        - checkpoint_path: 模型权重路径，可以是绝对路径或相对路径

        返回:
        - bool: 是否成功加载
        """
        if os.path.exists(checkpoint_path):
            try:
                # 加载权重
                checkpoint = torch.load(checkpoint_path, weights_only=False)

                # 过滤掉不需要的统计键
                if isinstance(checkpoint, dict):
                    if 'refine_head' in checkpoint:
                        filtered_dict = {k: v for k, v in checkpoint['refine_head'].items()
                                         if 'total_ops' not in k and 'total_params' not in k}
                    else:
                        filtered_dict = {k: v for k, v in checkpoint.items()
                                         if 'total_ops' not in k and 'total_params' not in k}

                    # 使用strict=False加载
                    self.refine_head.load_state_dict(filtered_dict, strict=False)
                    print(f"成功加载refine_head权重: {checkpoint_path}")
                    return True
                else:
                    print("模型格式不正确，无法加载")
                    return False
            except Exception as e:
                print(f"加载refine_head权重时出错: {e}")
                return False
        else:
            print(f"refine_head权重文件不存在: {checkpoint_path}")
            return False

    def save(self, prefix='best', save_dir='home'):
        """保存特征提取器和refine_head

        参数:
        - prefix: 保存文件前缀
        - save_dir: 保存目录

        返回:
        - bool: 是否成功保存
        """
        # 确保目录存在
        model_dir = os.path.join(save_dir, 'models')
        os.makedirs(model_dir, exist_ok=True)

        # 保存refine_head
        refine_head_path = os.path.join(model_dir, f'{prefix}_refine_head.pth')
        torch.save(self.refine_head.state_dict(), refine_head_path)

        print(f"特征提取器refine_head已保存到 {refine_head_path}")
        return True


# 辅助函数
def sigmoid(x):
    """Sigmoid函数，用于计算同步率"""
    return 1 / (1 + np.exp(-10 * x))  # 使用较陡的sigmoid函数


# 忆阻器启发的经验样本类
class MemristiveExperience:
    def __init__(self, state, action, reward, next_state, td_error=0.0):
        # 基础经验数据
        self.state = state  # 状态向量
        self.action = action  # 动作
        self.reward = reward  # 奖励
        self.next_state = next_state  # 下一状态

        # 忆阻器特性相关属性
        self.memory_strength = min(1.0, 0.3 + 0.7 * abs(td_error) / 10.0)  # 初始记忆强度
        self.age = 0  # 样本年龄
        self.last_used = 0  # 上次使用时间戳
        self.td_error_history = [abs(td_error)] if td_error != 0.0 else [0.0]  # TD误差历史
        self.use_count = 0  # 使用次数


# 忆阻器启发的时效经验回放池
class MemristiveReplayBuffer:
    def __init__(self, active_capacity=3000, archive_capacity=2000,
                 strength_threshold=0.2, decay_rate=0.001, enhancement_rate=0.1):
        # 缓冲区配置
        self.active_capacity = active_capacity  # 活跃区容量
        self.archive_capacity = archive_capacity  # 归档区容量
        self.active_buffer = []  # 活跃区样本列表
        self.archive_buffer = []  # 归档区样本列表

        # 忆阻器参数
        self.strength_threshold = strength_threshold  # 活跃区/归档区分界阈值
        self.decay_rate = decay_rate  # 记忆强度衰减率
        self.enhancement_rate = enhancement_rate  # 记忆强度增强率

        # 内部计数器和状态
        self.global_step = 0  # 全局步数
        self.total_samples = 0  # 总样本数
        self.active_sum_strength = 0.0  # 活跃区强度总和
        self.archive_sum_strength = 0.0  # 归档区强度总和

    def add(self, state, action, reward, next_state, td_error=0.0):
        """添加新样本到经验池"""
        # 创建新经验样本
        experience = MemristiveExperience(state, action, reward, next_state, td_error)

        # 确定添加位置
        if len(self.active_buffer) + len(self.archive_buffer) < self.active_capacity + self.archive_capacity:
            # 如果缓冲区未满，添加到活跃区
            self.active_buffer.append(experience)
            self.active_sum_strength += experience.memory_strength
        else:
            # 如果归档区有足够多样本且有低强度样本，替换归档区样本
            if len(self.archive_buffer) > 0:
                # 寻找归档区中记忆强度最低的样本
                min_idx = self._find_min_strength_index(self.archive_buffer)
                if self.archive_buffer[min_idx].memory_strength < experience.memory_strength:
                    self.archive_sum_strength -= self.archive_buffer[min_idx].memory_strength
                    self.archive_buffer[min_idx] = experience
                    self.archive_sum_strength += experience.memory_strength
                else:
                    # 如果新样本强度不足以替换，查找活跃区最弱样本
                    min_idx = self._find_min_strength_index(self.active_buffer)
                    if self.active_buffer[min_idx].memory_strength < experience.memory_strength:
                        self.active_sum_strength -= self.active_buffer[min_idx].memory_strength
                        self.active_buffer[min_idx] = experience
                        self.active_sum_strength += experience.memory_strength
            else:
                # 归档区为空，直接替换活跃区最弱样本
                min_idx = self._find_min_strength_index(self.active_buffer)
                self.active_sum_strength -= self.active_buffer[min_idx].memory_strength
                self.active_buffer[min_idx] = experience
                self.active_sum_strength += experience.memory_strength

        self.total_samples += 1

    def sample(self, batch_size, active_ratio=0.9):
        """基于记忆强度采样批次"""
        # 计算活跃区和归档区的采样数量
        active_size = min(int(batch_size * active_ratio), len(self.active_buffer))
        archive_size = min(batch_size - active_size, len(self.archive_buffer))

        # 构建活跃区采样概率
        active_probs = np.array([exp.memory_strength for exp in self.active_buffer])
        active_probs = active_probs / np.sum(active_probs) if np.sum(active_probs) > 0 else None

        # 构建归档区采样概率
        archive_probs = np.array([exp.memory_strength for exp in self.archive_buffer]) if len(
            self.archive_buffer) > 0 else None
        archive_probs = archive_probs / np.sum(archive_probs) if archive_probs is not None and np.sum(
            archive_probs) > 0 else None

        # 采样活跃区
        active_batch = []
        active_indices = []
        if active_size > 0 and len(self.active_buffer) > 0:
            active_indices = np.random.choice(len(self.active_buffer), active_size,
                                              p=active_probs, replace=False)
            active_batch = [self.active_buffer[i] for i in active_indices]

        # 采样归档区
        archive_batch = []
        archive_indices = []
        if archive_size > 0 and len(self.archive_buffer) > 0 and archive_probs is not None:
            archive_indices = np.random.choice(len(self.archive_buffer), archive_size,
                                               p=archive_probs, replace=False)
            archive_batch = [self.archive_buffer[i] for i in archive_indices]

        # 合并批次
        batch = active_batch + archive_batch
        batch_indices = [(0, i) for i in active_indices] + [(1, i) for i in archive_indices]

        # 更新采样样本的使用信息
        self.global_step += 1
        for exp in batch:
            exp.last_used = self.global_step
            exp.use_count += 1

        return batch, batch_indices

    def update_strengths(self, indices, td_errors):
        """根据TD误差更新样本记忆强度"""
        for (buffer_type, idx), td_error in zip(indices, td_errors):
            # 获取正确的缓冲区
            buffer = self.active_buffer if buffer_type == 0 else self.archive_buffer

            # 更新样本的TD误差历史
            buffer[idx].td_error_history.append(abs(td_error))
            if len(buffer[idx].td_error_history) > 5:
                buffer[idx].td_error_history.pop(0)

            # 更新记忆强度
            old_strength = buffer[idx].memory_strength
            # 基于TD误差增强记忆强度
            enhancement = self.enhancement_rate * abs(td_error)
            buffer[idx].memory_strength = min(1.0, buffer[idx].memory_strength + enhancement)

            # 更新强度总和
            if buffer_type == 0:
                self.active_sum_strength += (buffer[idx].memory_strength - old_strength)
            else:
                self.archive_sum_strength += (buffer[idx].memory_strength - old_strength)

    def apply_time_decay(self):
        """应用全局时效衰减并移动样本"""
        # 活跃区衰减与迁移
        samples_to_archive = []
        for i in range(len(self.active_buffer)):
            # 增加年龄
            self.active_buffer[i].age += 1

            # 应用衰减
            old_strength = self.active_buffer[i].memory_strength
            self.active_buffer[i].memory_strength *= (1.0 - self.decay_rate)
            self.active_sum_strength -= (old_strength - self.active_buffer[i].memory_strength)

            # 检查是否需要迁移到归档区
            if self.active_buffer[i].memory_strength < self.strength_threshold:
                samples_to_archive.append(i)

        # 从活跃区移动到归档区
        if len(samples_to_archive) > 0 and len(self.archive_buffer) < self.archive_capacity:
            # 从后向前移除，避免索引混乱
            for i in sorted(samples_to_archive, reverse=True):
                if len(self.archive_buffer) < self.archive_capacity:
                    exp = self.active_buffer.pop(i)
                    self.archive_buffer.append(exp)
                    self.active_sum_strength -= exp.memory_strength
                    self.archive_sum_strength += exp.memory_strength

        # 归档区衰减
        for i in range(len(self.archive_buffer)):
            self.archive_buffer[i].age += 1
            old_strength = self.archive_buffer[i].memory_strength
            self.archive_buffer[i].memory_strength *= (1.0 - self.decay_rate * 1.5)  # 归档区衰减更快
            self.archive_sum_strength -= (old_strength - self.archive_buffer[i].memory_strength)

    def reactivate_samples(self, count=50):
        """重新激活一些归档样本"""
        if len(self.archive_buffer) == 0 or len(self.active_buffer) >= self.active_capacity:
            return 0

        # 随机选择一些归档样本
        indices = np.random.choice(len(self.archive_buffer),
                                   min(count, len(self.archive_buffer)), replace=False)

        # 移动到活跃区
        moved_count = 0
        for i in sorted(indices, reverse=True):
            if len(self.active_buffer) < self.active_capacity:
                exp = self.archive_buffer.pop(i)
                # 提升其记忆强度作为重激活
                old_strength = exp.memory_strength
                exp.memory_strength = min(1.0, exp.memory_strength * 2.0)

                self.active_buffer.append(exp)
                self.archive_sum_strength -= old_strength
                self.active_sum_strength += exp.memory_strength
                moved_count += 1

        return moved_count

    def _find_min_strength_index(self, buffer):
        """查找缓冲区中记忆强度最低的样本索引"""
        if len(buffer) == 0:
            return None

        min_strength = float('inf')
        min_idx = 0

        for i, exp in enumerate(buffer):
            if exp.memory_strength < min_strength:
                min_strength = exp.memory_strength
                min_idx = i

        return min_idx

    def get_stats(self):
        """获取经验池状态统计信息"""
        stats = {
            'active_size': len(self.active_buffer),
            'archive_size': len(self.archive_buffer),
            'total_samples': self.total_samples,
            'active_avg_strength': self.active_sum_strength / max(1, len(self.active_buffer)),
            'archive_avg_strength': self.archive_sum_strength / max(1, len(self.archive_buffer)),
            'active_strengths': [exp.memory_strength for exp in self.active_buffer],
            'archive_strengths': [exp.memory_strength for exp in self.archive_buffer]
        }

        return stats

    def __len__(self):
        """返回经验池中样本总数"""
        return len(self.active_buffer) + len(self.archive_buffer)


# 添加忆阻器增强的DQN类，放在MobileFeatureExtractor类后面
class DQN(nn.Module):
    def __init__(self, device, state_dim=4096, memory_capacity=10000, batch_size=32,
                 lr=1e-4, gamma=0.9, q_network_iteration=50):
        super(DQN, self).__init__()
        self.device = device
        self.state_dim = state_dim
        self.memory_capacity = memory_capacity
        self.batch_size = batch_size
        self.lr = lr
        self.gamma = gamma
        self.q_network_iteration = q_network_iteration

        # 设置随机种子以提高稳定性
        torch.manual_seed(1)
        np.random.seed(1)

        # 评估网络 - 快速适应
        self.eval_net = nn.Sequential(
            MemristorLayer(state_dim, 1024, decay_rate=0.03, importance_rate=0.015, activation_strength=0.15,
                           is_target=False),
            nn.ReLU(),
            nn.Dropout(0.2),
            MemristorLayer(1024, 1024, decay_rate=0.04, importance_rate=0.02, activation_strength=0.12,
                           is_target=False),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(1024, 9)
        ).to(device)

        # 目标网络 - 稳定记忆
        self.target_net = nn.Sequential(
            MemristorLayer(state_dim, 1024, decay_rate=0.003, importance_rate=0.001, activation_strength=0.05,
                           is_target=True),
            nn.ReLU(),
            nn.Dropout(0.1),
            MemristorLayer(1024, 1024, decay_rate=0.002, importance_rate=0.0008, activation_strength=0.04,
                           is_target=True),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(1024, 9)
        ).to(device)

        # 从评估网络初始化目标网络
        self.update_target_net(tau=1.0)

        # 训练相关参数
        self.learn_step_counter = 0
        self.memory_counter = 0
        self.memory = np.zeros((memory_capacity, state_dim * 2 + 2))  # 保留原有经验回放
        self.optimizer = torch.optim.Adam(self.eval_net.parameters(), lr=lr, weight_decay=1e-5)
        self.loss_func = nn.MSELoss()
        self.train_loss = []
        self.learn_step = []
        self.recent_td_errors = []  # 用于跟踪TD误差
        self.recent_rewards = []  # 用于监控学习进展

        # 参数变化历史，用于萌发可塑性
        self.prev_eval_weights = [p.data.clone() for p in self.eval_net.parameters()]
        self.sync_threshold = 0.3  # 同步阈值

    def forward(self, x):
        """前向传播（用于评估网络）"""
        return self.eval_net(x)

    def choose_action(self, state, epsilon=0):
        """选择动作，使用评估网络"""
        # 检查state是否已经是tensor
        if isinstance(state, torch.Tensor):
            # 如果已经是tensor，确保维度正确并且在正确的设备上
            if state.dim() == 1:
                state = state.unsqueeze(0)
            # 确保在正确的设备上
            state = state.to(self.device)
        else:
            # 如果不是tensor，转换为tensor
            state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        if np.random.uniform() <= epsilon:
            action = np.random.randint(0, 9)  # 随机策略
        else:
            with torch.no_grad():
                action_value = self.eval_net(state)
                action = torch.max(action_value, 1)[1].cpu().item()  # 贪婪策略
        return action

    def store_transition(self, state, action, reward, next_state):
        """存储经验，保留原有经验回放机制"""
        transition = np.hstack((state, [action, reward], next_state))
        index = self.memory_counter % self.memory_capacity
        self.memory[index, :] = transition
        self.memory_counter += 1

        # 记录最近的奖励，用于学习进展监控
        self.recent_rewards.append(reward)
        if len(self.recent_rewards) > 100:
            self.recent_rewards.pop(0)

    def learn(self):
        """学习过程，使用忆阻器增强的目标网络"""
        # 保存当前权重，用于萌发可塑性计算
        current_weights = [p.data.clone() for p in self.eval_net.parameters()]

        # 目标网络参数更新频率
        if self.learn_step_counter % self.q_network_iteration == 0:
            # 应用选择性同步，而非简单复制
            self.selective_sync()

        # 忆阻器记忆衰减（动态频率）
        decay_interval = max(20, 100 - int(self.memory_counter / self.memory_capacity * 80))
        if self.learn_step_counter % decay_interval == 0:
            self.apply_memory_decay()

        # 检查是否有足够的样本
        if self.memory_counter < self.batch_size:
            # 样本不足，跳过本次学习
            self.learn_step_counter += 1
            return

        # 从经验池采样
        if self.memory_counter > self.memory_capacity:
            sample_index = np.random.choice(self.memory_capacity, self.batch_size)
        else:
            sample_index = np.random.choice(self.memory_counter, self.batch_size)
        batch_memory = self.memory[sample_index, :]

        # 准备批次数据
        batch_state = torch.FloatTensor(np.array(batch_memory[:, :self.state_dim])).to(self.device)
        batch_action = torch.LongTensor(np.array(batch_memory[:, self.state_dim:self.state_dim + 1].astype(int))).to(
            self.device)
        batch_reward = torch.FloatTensor(np.array(batch_memory[:, self.state_dim + 1:self.state_dim + 2])).to(
            self.device)
        batch_next_state = torch.FloatTensor(np.array(batch_memory[:, -self.state_dim:])).to(self.device)

        # 确保eval_net处于训练模式
        self.eval_net.train()

        # 计算Q值
        q_eval = self.eval_net(batch_state).gather(1, batch_action)

        # 计算目标Q值，保持梯度信息
        q_next = self.target_net(batch_next_state)
        q_target = batch_reward + self.gamma * q_next.max(1)[0].view(-1, 1)

        # 计算TD误差，用于更新忆阻器重要性
        td_errors = (q_target.detach() - q_eval.detach()).cpu().numpy()
        mean_td_error = np.mean(np.abs(td_errors))
        self.recent_td_errors.append(mean_td_error)

        # 更新忆阻器重要性
        self.update_importance(mean_td_error)

        # 计算损失并优化
        loss = self.loss_func(q_eval, q_target)
        self.train_loss.append(loss.item())
        self.learn_step.append(self.learn_step_counter)

        # 检查损失值是否合法
        if torch.isnan(loss) or torch.isinf(loss):
            print(f"警告：损失值异常 - {loss.item()}")
            self.learn_step_counter += 1
            return

        self.optimizer.zero_grad()

        try:
            loss.backward()
            # 梯度裁剪，提高稳定性
            torch.nn.utils.clip_grad_norm_(self.eval_net.parameters(), max_norm=1.0)
            self.optimizer.step()
        except RuntimeError as e:
            print(f"反向传播错误: {e}")
            # 打印debug信息
            print(f"q_eval shape: {q_eval.shape}, requires_grad: {q_eval.requires_grad}")
            print(f"q_target shape: {q_target.shape}, requires_grad: {q_target.requires_grad}")
            # 如果出错，我们仍然更新计数器但不执行优化
            self.learn_step_counter += 1
            return

        # 更新萌发可塑性参数
        self.update_metaplasticity(current_weights)

        # 保存当前权重用于下次比较
        self.prev_eval_weights = [p.data.clone() for p in self.eval_net.parameters()]

        self.learn_step_counter += 1

    def apply_memory_decay(self):
        """应用忆阻器记忆衰减"""
        # 衰减速率根据最近奖励进行动态调整
        avg_reward = np.mean(self.recent_rewards) if self.recent_rewards else 0
        reward_factor = 1.2 if avg_reward > 0 else 0.8

        for module in self.eval_net.modules():
            if isinstance(module, MemristorLayer):
                # 为评估网络应用正常衰减
                module.apply_decay()

        # 目标网络衰减频率较低且有条件
        if self.learn_step_counter % 100 == 0:
            for module in self.target_net.modules():
                if isinstance(module, MemristorLayer):
                    # 为目标网络应用调整后的衰减
                    effective_decay_rate = module.decay_rate * reward_factor
                    with torch.no_grad():
                        module.memory_strength *= (1 - effective_decay_rate)

    def update_importance(self, td_error):
        """更新忆阻器层的重要性"""
        # 根据TD误差计算重要性调整因子 - 大误差给予更大权重
        importance_factor = min(1.5, max(0.5, 1.0 + td_error))

        for module in self.eval_net.modules():
            if isinstance(module, MemristorLayer):
                # 为评估网络更新重要性
                effective_rate = module.importance_rate * importance_factor
                with torch.no_grad():
                    importance_update = effective_rate * abs(td_error)
                    module.importance += importance_update

        # 目标网络重要性更新频率动态调整
        update_freq = int(max(20, 50 - td_error * 100))  # TD误差大时更频繁更新
        if self.learn_step_counter % update_freq == 0:
            for module in self.target_net.modules():
                if isinstance(module, MemristorLayer):
                    # 为目标网络更新重要性，更保守
                    with torch.no_grad():
                        importance_update = module.importance_rate * 0.15 * abs(td_error)
                        module.importance += importance_update

    def update_metaplasticity(self, current_weights):
        """更新萌发可塑性参数，增强学习的稳定性和适应性"""
        i = 0
        for module in self.eval_net.modules():
            if isinstance(module, MemristorLayer) and i < len(self.prev_eval_weights):
                with torch.no_grad():
                    # 计算当前变化量
                    current_change = torch.abs(module.weight.data - self.prev_eval_weights[i])

                    # 更新参数变化历史
                    module.param_change_history = (
                            module.momentum * module.param_change_history +
                            (1 - module.momentum) * current_change
                    )

                    # 检测变化剧烈的区域 - 可能需要稳定
                    change_threshold = module.param_change_history.mean() * 2
                    unstable_regions = current_change > change_threshold

                    # 对变化剧烈的区域进行稳定化处理
                    if unstable_regions.any():
                        # 计算稳定系数 - 变化越大，稳定性越强
                        stability_factor = torch.clamp(
                            current_change / (module.param_change_history + 1e-6),
                            min=0.1, max=2.0
                        )

                        # 对不稳定区域增强记忆强度，提高稳定性
                        memory_boost = torch.zeros_like(module.memory_strength)
                        memory_boost[unstable_regions] = 0.05 * stability_factor[unstable_regions]
                        module.memory_strength += memory_boost

                i += 1

    def selective_sync(self):
        """
        选择性同步：不是盲目复制所有参数，而是根据重要性和稳定性进行选择性同步
        """
        # 使用no_grad模式执行同步，避免计算图构建
        with torch.no_grad():
            src_tensors = [module for module in self.eval_net.modules() if isinstance(module, MemristorLayer)]
            target_tensors = [module for module in self.target_net.modules() if isinstance(module, MemristorLayer)]

            if len(src_tensors) != len(target_tensors):
                print("警告：源网络和目标网络结构不匹配，执行全部同步")
                self.update_target_net(tau=1.0)
                return

            # 根据萌发可塑性和重要性确定同步策略
            for eval_layer, target_layer in zip(src_tensors, target_tensors):
                # 提取当前的权重变化历史和重要性
                weight_changes = eval_layer.param_change_history
                weight_importance = torch.sigmoid(eval_layer.importance)

                # 计算权重变化和重要性的乘积，作为同步决策依据
                sync_priority = weight_changes * weight_importance

                # 为每个权重设置同步掩码：变化大且重要性高的参数会被同步
                sync_mask = (sync_priority > self.sync_threshold).float()

                # 应用同步掩码，将选定的参数从评估网络复制到目标网络
                target_layer.weight.data = (
                        (1 - sync_mask) * target_layer.weight.data +
                        sync_mask * eval_layer.weight.data
                )

                # 偏置项可以完全同步，它们对稳定性影响较小
                target_layer.bias.data.copy_(eval_layer.bias.data)

                # 同步记忆强度，但比例较低以保持稳定性
                memory_sync_ratio = 0.3  # 只同步30%的记忆强度
                target_layer.memory_strength.data = (
                        (1 - memory_sync_ratio) * target_layer.memory_strength.data +
                        memory_sync_ratio * eval_layer.memory_strength.data
                )

                # 重要性可以完全同步，它反映了参数的全局重要性
                target_layer.importance.data.copy_(eval_layer.importance.data)

            # 对于非忆阻器层（如最后的线性层），直接同步
            linear_layers_eval = [module for module in self.eval_net.modules()
                                  if isinstance(module, nn.Linear) and
                                  not any(module is layer for layer in src_tensors)]

            linear_layers_target = [module for module in self.target_net.modules()
                                    if isinstance(module, nn.Linear) and
                                    not any(module is layer for layer in target_tensors)]

            for src_linear, target_linear in zip(linear_layers_eval, linear_layers_target):
                target_linear.weight.data.copy_(src_linear.weight.data)
                target_linear.bias.data.copy_(src_linear.bias.data)

        # 打印同步信息
        print(f"阶段性同步完成，当前学习步数: {self.learn_step_counter}")

    def update_target_net(self, tau=0.001):
        """软更新目标网络"""
        for target_param, eval_param in zip(self.target_net.parameters(), self.eval_net.parameters()):
            target_param.data.copy_(tau * eval_param.data + (1.0 - tau) * target_param.data)

    def save(self, save_dir='./RDQN_models', prefix='best', hilbert_mapper=None):
        """保存模型"""
        # 确保目录存在
        os.makedirs(save_dir, exist_ok=True)

        # 保存完整模型（包含忆阻器特性）
        model_path = os.path.join(save_dir, f'{prefix}_rdqn_net.pth')

        # 准备保存的状态字典
        save_dict = {
            'eval_net': self.eval_net.state_dict(),
            'target_net': self.target_net.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'learn_step_counter': self.learn_step_counter,
            'train_loss': self.train_loss,
            'learn_step': self.learn_step
        }
        
        # 如果提供了希尔伯特映射器，将其保存到模型中
        if hilbert_mapper is not None:
            save_dict['hilbert_mapper'] = hilbert_mapper.state_dict()
            print(f"希尔伯特映射器权重已添加到DQN模型中")

        # 保存模型
        torch.save(save_dict, model_path)

        print(f"RDQN模型已保存到 {model_path}")

        return True

    def load(self, path, memory_path=None):
        """
        加载模型权重

        参数:
        - path: 模型路径
        - memory_path: 记忆库路径，默认为None
        """
        try:
            # 加载模型权重
            checkpoint = torch.load(path, weights_only=False)

            # 如果是完整模型字典，提取eval_net部分
            if isinstance(checkpoint, dict) and not 'model_state_dict' in checkpoint:
                # 新式保存格式 - 完整字典
                if 'eval_net' in checkpoint:
                    # 过滤掉统计键
                    filtered_state_dict = {k: v for k, v in checkpoint['eval_net'].items() 
                                         if 'total_ops' not in k and 'total_params' not in k}
                    self.eval_net.load_state_dict(filtered_state_dict, strict=False)
                    self.target_net.load_state_dict(filtered_state_dict, strict=False)  # 同步到目标网络
                elif 'dqn' in checkpoint:
                    # 兼容旧式格式
                    filtered_state_dict = {k: v for k, v in checkpoint['dqn'].items() 
                                         if 'total_ops' not in k and 'total_params' not in k}
                    self.eval_net.load_state_dict(filtered_state_dict, strict=False)
                    self.target_net.load_state_dict(filtered_state_dict, strict=False)  # 同步到目标网络
                else:
                    # 假设是直接的状态字典
                    filtered_state_dict = {k: v for k, v in checkpoint.items() 
                                         if 'total_ops' not in k and 'total_params' not in k}
                    self.eval_net.load_state_dict(filtered_state_dict, strict=False)
                    self.target_net.load_state_dict(filtered_state_dict, strict=False)  # 同步到目标网络
            else:
                # 旧式保存格式 - 直接状态字典
                filtered_state_dict = {k: v for k, v in checkpoint.items() 
                                     if 'total_ops' not in k and 'total_params' not in k}
                self.eval_net.load_state_dict(filtered_state_dict, strict=False)
                self.target_net.load_state_dict(filtered_state_dict, strict=False)  # 同步到目标网络

            print(f"成功加载DQN模型: {path}")

            # 如果提供了记忆库路径，尝试加载记忆库
            if memory_path and os.path.exists(memory_path):
                try:
                    memory_data = torch.load(memory_path, weights_only=False)
                    if isinstance(memory_data, dict) and 'memory' in memory_data:
                        self.memory = memory_data['memory']
                        self.memory_counter = memory_data['memory_counter']
                        print(f"成功加载记忆库: {memory_path}")
                except Exception as e:
                    print(f"加载记忆库时出错: {e}")

            return True
        except Exception as e:
            print(f"加载DQN模型时出错: {e}")
            return False


def calculate_model_complexity(model_name, model, input_shape=None):
    if not has_calflops:
        print(f"无法计算{model_name}的FLOPs，需要安装calflops库")
        return

    try:
        if input_shape is None:
            # 默认使用一个小批量大小和常见的输入尺寸
            if isinstance(model, nn.Sequential) and "DQN" in model_name:
                # 针对DQN模型，输入形状应该是(batch_size, state_dim)
                input_shape = (1, NUM_STATES)
                # 创建一个具有正确形状的随机输入张量传递给calculate_flops
                dummy_input = torch.randn(input_shape)
                flops, macs, params = calculate_flops(
                    model=model,
                    args=[dummy_input],  # 使用args参数而不是input_shape
                    output_as_string=True,
                    output_precision=4,
                    print_results=True,
                    print_detailed=True
                )
            else:
                # 针对特征提取器，输入为图像
                input_shape = (1, 3, 128, 128)
                flops, macs, params = calculate_flops(
                    model=model,
                    input_shape=input_shape,
                    output_as_string=True,
                    output_precision=4,
                    print_results=True,
                    print_detailed=True
                )
        else:
            flops, macs, params = calculate_flops(
                model=model,
                input_shape=input_shape,
                output_as_string=True,
                output_precision=4,
                print_results=True,
                print_detailed=True
            )

        print(f"\n{model_name}模型复杂度: FLOPs={flops}, MACs={macs}, Params={params}\n")
    except Exception as e:
        print(f"计算{model_name}的FLOPs时出错: {e}")

        # 为避免中断训练，捕获异常后继续
        print(f"由于错误，跳过{model_name}的FLOPs计算，继续训练...") 