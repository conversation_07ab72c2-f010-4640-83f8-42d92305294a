#!/usr/bin/env python3
"""
验证DQN变体集成的完整性
检查所有集成的DQN变体是否能正确导入和初始化指标记录器
"""

import os
import sys
import importlib.util

def check_integration(variant_name, variant_dir):
    """检查单个DQN变体的集成状态"""
    print(f"\n🔍 检查 {variant_name} 集成状态...")
    
    # 检查目录是否存在
    if not os.path.exists(variant_dir):
        print(f"❌ 目录不存在: {variant_dir}")
        return False
    
    # 检查train.py是否存在
    train_file = os.path.join(variant_dir, "train.py")
    if not os.path.exists(train_file):
        print(f"❌ train.py不存在: {train_file}")
        return False
    
    # 检查是否包含指标记录器导入
    try:
        with open(train_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        checks = {
            "导入指标记录器": "from dqn_metrics_recorder import DQNMetricsRecorder" in content,
            "初始化记录器": f'DQNMetricsRecorder("{variant_name}"' in content,
            "记录epoch数据": "metrics_recorder.record_epoch" in content,
            "记录评估数据": "metrics_recorder.record_evaluation" in content,
            "完成训练记录": "metrics_recorder.finalize_training" in content,
            "收集动作数据": "epoch_actions.append(action)" in content
        }
        
        all_passed = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print(f"✅ {variant_name} 集成完整")
            return True
        else:
            print(f"⚠️ {variant_name} 集成不完整")
            return False
            
    except Exception as e:
        print(f"❌ 检查 {variant_name} 时出错: {e}")
        return False

def check_core_files():
    """检查核心文件是否存在"""
    print("🔍 检查核心文件...")
    
    core_files = {
        "指标记录器": "dqn_metrics_recorder.py",
        "分析制图器": "dqn_analysis_plotter.py",
        "测试脚本": "test_integration.py"
    }
    
    all_exist = True
    for name, file_path in core_files.items():
        if os.path.exists(file_path):
            print(f"  ✅ {name}: {file_path}")
        else:
            print(f"  ❌ {name}: {file_path} (不存在)")
            all_exist = False
    
    return all_exist

def test_import():
    """测试导入功能"""
    print("\n🧪 测试导入功能...")
    
    try:
        from dqn_metrics_recorder import DQNMetricsRecorder
        print("  ✅ 成功导入 DQNMetricsRecorder")
        
        # 测试创建记录器
        recorder = DQNMetricsRecorder("TestDQN", "test_metrics_temp")
        print("  ✅ 成功创建指标记录器")
        
        # 清理测试目录
        import shutil
        if os.path.exists("test_metrics_temp"):
            shutil.rmtree("test_metrics_temp")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🎯 DQN变体集成验证")
    print("=" * 60)
    
    # 检查核心文件
    core_ok = check_core_files()
    
    # 检查各个DQN变体的集成
    variants = [
        ("DQN", "DQN"),
        ("DoubleDQN", "DoubleDQN"),
        ("DuelingDQN", "DuelingDQN"),
        ("EWC_DQN", "EWC_DQN"),
        ("LSTM_DQN", "LSTM_DQN"),
        ("PER_DQN", "PER_DQN"),
        ("RDQN", "RDQN"),
        ("Attention_DQN", "Attention_DQN"),
        ("RDQN_Ablation", "RDQN_Ablation")  # 消融实验目录
    ]
    
    integration_results = []
    for variant_name, variant_dir in variants:
        result = check_integration(variant_name, variant_dir)
        integration_results.append((variant_name, result))
    
    # 测试导入功能
    import_ok = test_import()
    
    # 总结报告
    print("\n" + "=" * 60)
    print("📋 验证总结报告")
    print("=" * 60)
    
    print(f"\n🔧 核心文件: {'✅ 完整' if core_ok else '❌ 缺失'}")
    print(f"📦 导入功能: {'✅ 正常' if import_ok else '❌ 异常'}")
    
    print(f"\n📊 DQN变体集成状态:")
    success_count = 0
    for variant_name, result in integration_results:
        status = "✅ 已集成" if result else "❌ 未集成"
        print(f"  {variant_name:<15}: {status}")
        if result:
            success_count += 1
    
    print(f"\n📈 集成进度: {success_count}/{len(variants)} ({success_count/len(variants)*100:.1f}%)")
    
    if success_count == len(variants) and core_ok and import_ok:
        print("\n🎉 所有检查通过！系统已准备就绪")
        print("\n🚀 下一步操作:")
        print("1. 手动训练各个DQN变体:")
        for variant_name, _ in variants:
            print(f"   cd {variant_name} && python train.py --epochs 50")
        print("\n2. 生成对比分析:")
        print("   python dqn_analysis_plotter.py")
        
    else:
        print("\n⚠️ 发现问题，请检查上述错误信息")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
