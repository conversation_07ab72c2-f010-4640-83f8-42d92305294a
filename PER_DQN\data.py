import xml.etree.ElementTree as ET
import numpy as np
import os


def load_images_names_in_data_set(data_set_name, path_voc):  # 读取文件名
    # 检查是否是测试集数据
    is_test_dataset = 'test' in data_set_name.lower()

    if is_test_dataset:
        # 测试集数据：优先查找VOCtest，然后查找VOC2012
        test_paths = [
            os.path.join(path_voc, 'VOCtest'),  # 独立的测试集目录
            os.path.join(path_voc, 'VOC2012')  # VOC2012中的测试集
        ]

        positive_image_names = []

        for test_path in test_paths:
            file_path = os.path.join(test_path, 'ImageSets/Main', data_set_name + '.txt')
            try:
                with open(file_path, 'r') as f:
                    image_names = f.readlines()
                    # 处理每行数据前确保格式一致
                    for line in image_names:
                        items = line.strip().split()
                        if len(items) > 1 and items[1] == '1':  # VOC数据集中1表示存在目标类别
                            positive_image_names.append(items[0])
                    print(f"从{file_path}加载了{len(positive_image_names)}个正样本图像")
                    break  # 找到文件就退出循环
            except FileNotFoundError:
                print(f"警告：未找到测试集文件: {file_path}")
                continue

        if not positive_image_names:
            print(f"错误：在所有路径中都未找到 {data_set_name}.txt")

        print(f"总共加载了{len(positive_image_names)}个正样本图像")
        return positive_image_names
    else:
        # 训练集路径 - 查找VOC2012和VOC2007
        base_path = path_voc  # 使用传入的路径参数
        path_voc2012 = os.path.join(base_path, 'VOC2012')
        file_path_voc2012 = os.path.join(path_voc2012, 'ImageSets/Main', data_set_name + '.txt')

        path_voc2007 = os.path.join(base_path, 'VOC2007')
        file_path_voc2007 = os.path.join(path_voc2007, 'ImageSets/Main', data_set_name + '.txt')

        positive_image_names = []

        # 读取VOC2012数据
        try:
            with open(file_path_voc2012, 'r') as f:
                image_names = f.readlines()
                # 处理每行数据前确保格式一致
                for line in image_names:
                    items = line.strip().split()
                    if len(items) > 1 and items[1] == '1':  # VOC数据集中1表示存在目标类别
                        positive_image_names.append(items[0])
                print(f"从VOC2012加载了{len(positive_image_names)}个正样本图像")
        except FileNotFoundError:
            print(f"警告：未找到VOC2012文件: {file_path_voc2012}")

        # 读取VOC2007数据
        voc2007_count = len(positive_image_names)  # 记录加载VOC2012后的计数
        try:
            with open(file_path_voc2007, 'r') as f:
                image_names = f.readlines()
                # 直接处理每行，避免np.array转换问题
                for line in image_names:
                    items = line.strip().split()
                    if len(items) > 1 and items[1] == '1':  # VOC数据集中1表示存在目标类别
                        positive_image_names.append(items[0])
                print(f"从VOC2007加载了{len(positive_image_names) - voc2007_count}个正样本图像")
        except FileNotFoundError:
            print(f"警告：未找到VOC2007文件: {file_path_voc2007}")

        print(f"总共加载了{len(positive_image_names)}个正样本图像")
        return positive_image_names


def get_bb_of_gt_from_pascal_xml_annotation(xml_name, voc_path):  # 用来读取Annotations目录中的xml文件,得到目标的名字与目标框
    # 智能检测XML文件路径：优先测试集，然后训练集
    possible_paths = [
        # 测试集路径
        os.path.join(voc_path, 'VOCtest', 'Annotations', xml_name + '.xml'),
        os.path.join(voc_path, 'VOC2012', 'Annotations', xml_name + '.xml'),  # VOC2012测试集
        # 训练集路径
        os.path.join(voc_path, 'VOC2012', 'Annotations', xml_name + '.xml'),
        os.path.join(voc_path, 'VOC2007', 'Annotations', xml_name + '.xml'),
    ]

    # 查找存在的XML文件
    string = None
    for path in possible_paths:
        if os.path.exists(path):
            string = path
            break

    # 如果所有路径都不存在，记录警告并返回空数组
    if string is None:
        print(f"警告：无法找到XML文件: {xml_name}.xml")
        print(f"  已尝试路径:")
        for path in possible_paths:
            print(f"    - {path}")
        return np.zeros([0, 5])  # 返回空数组

    tree = ET.parse(string)
    root = tree.getroot()
    names = []
    x_min = []
    x_max = []
    y_min = []
    y_max = []
    for child in root:
        if child.tag == 'object':
            for child2 in child:
                if child2.tag == 'name':
                    names.append(child2.text)
                elif child2.tag == 'bndbox':
                    for child3 in child2:
                        if child3.tag == 'xmin':
                            x_min.append(child3.text)
                        elif child3.tag == 'xmax':
                            x_max.append(child3.text)
                        elif child3.tag == 'ymin':
                            y_min.append(child3.text)
                        elif child3.tag == 'ymax':
                            y_max.append(child3.text)
    category_and_bb = np.zeros([np.size(names), 5])
    for i in range(np.size(names)):
        if names[i] == 'aeroplane':
            category_and_bb[i][0] = get_id_of_class_name(names[i])
            category_and_bb[i][1] = x_min[i]
            category_and_bb[i][2] = x_max[i]
            category_and_bb[i][3] = y_min[i]
            category_and_bb[i][4] = y_max[i]
    return category_and_bb


def get_bb_of_gt_from_pascal_xml_annotationl(xml_name, voc_path):  # 用来读取Annotations目录中的xml文件,得到目标的名字与目标框
    # 智能检测XML文件路径：优先测试集，然后训练集
    possible_paths = [
        # 测试集路径
        os.path.join(voc_path, 'VOCtest', 'Annotations', xml_name + '.xml'),
        os.path.join(voc_path, 'VOC2012', 'Annotations', xml_name + '.xml'),  # VOC2012测试集
        # 训练集路径
        os.path.join(voc_path, 'VOC2012', 'Annotations', xml_name + '.xml'),
        os.path.join(voc_path, 'VOC2007', 'Annotations', xml_name + '.xml'),
    ]

    # 查找存在的XML文件
    string = None
    for path in possible_paths:
        if os.path.exists(path):
            string = path
            break

    # 如果所有路径都不存在，记录警告并返回空数组
    if string is None:
        print(f"警告：无法找到XML文件: {xml_name}.xml")
        return np.zeros([0, 5])  # 返回空数组

    tree = ET.parse(string)
    root = tree.getroot()
    names = []
    x_min = []
    x_max = []
    y_min = []
    y_max = []
    for child in root:
        if child.tag == 'object':
            for child2 in child:
                if child2.tag == 'name':
                    names.append(child2.text)
                elif child2.tag == 'bndbox':
                    for child3 in child2:
                        if child3.tag == 'xmin':
                            x_min.append(child3.text)
                        elif child3.tag == 'xmax':
                            x_max.append(child3.text)
                        elif child3.tag == 'ymin':
                            y_min.append(child3.text)
                        elif child3.tag == 'ymax':
                            y_max.append(child3.text)
    category_and_bb = np.zeros([np.size(names), 5])
    for i in range(np.size(names)):
        if names[i] == 'aeroplane':
            category_and_bb[i][0] = get_id_of_class_name(names[i])
            category_and_bb[i][1] = x_min[i]
            category_and_bb[i][2] = x_max[i]
            category_and_bb[i][3] = y_min[i]
            category_and_bb[i][4] = y_max[i]
    return category_and_bb


def get_all_annotations(image_names, voc_path):  # 得到所有图片的标注信息
    annotations = []
    # 确保image_names是一维列表或数组
    if isinstance(image_names, np.ndarray) and image_names.ndim > 1:
        # 如果是多维数组，转换为一维列表
        image_names_list = image_names.flatten().tolist()
    else:
        image_names_list = image_names

    for image_name in image_names_list:
        annotations.append(get_bb_of_gt_from_pascal_xml_annotation(image_name, voc_path))
    return annotations


def generate_bounding_box_from_annotation(annotation, image_shape):
    length_annotation = annotation.shape[0]  # shape[0]就是读取矩阵第一维度的长度
    masks = np.zeros([image_shape[0], image_shape[1], length_annotation])
    for i in range(0, length_annotation):
        masks[int(annotation[i, 3]):int(annotation[i, 4]), int(annotation[i, 1]):int(annotation[i, 2]), i] = 1
    return masks


def get_id_of_class_name(class_name):
    if class_name == 'aeroplane':
        return 1
    elif class_name == 'bicycle':
        return 2
    elif class_name == 'bird':
        return 3
    elif class_name == 'boat':
        return 4
    elif class_name == 'bottle':
        return 5
    elif class_name == 'bus':
        return 6
    elif class_name == 'car':
        return 7
    elif class_name == 'cat':
        return 8
    elif class_name == 'chair':
        return 9
    elif class_name == 'cow':
        return 10
    elif class_name == 'diningtable':
        return 11
    elif class_name == 'dog':
        return 12
    elif class_name == 'horse':
        return 13
    elif class_name == 'motorbike':
        return 14
    elif class_name == 'person':
        return 15
    elif class_name == 'pottedplant':
        return 16
    elif class_name == 'sheep':
        return 17
    elif class_name == 'sofa':
        return 18
    elif class_name == 'train':
        return 19
    elif class_name == 'tvmonitor':
        return 20


def get_image_path(image_name, voc_path):
    """
    智能查找图像文件路径
    优先查找测试集，然后查找训练集
    """
    possible_paths = [
        # 测试集路径
        os.path.join(voc_path, 'VOCtest', 'JPEGImages', image_name + '.jpg'),
        os.path.join(voc_path, 'VOC2012', 'JPEGImages', image_name + '.jpg'),  # VOC2012测试集
        # 训练集路径
        os.path.join(voc_path, 'VOC2012', 'JPEGImages', image_name + '.jpg'),
        os.path.join(voc_path, 'VOC2007', 'JPEGImages', image_name + '.jpg'),
    ]

    # 查找存在的图像文件
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果所有路径都不存在，返回None
    return None
