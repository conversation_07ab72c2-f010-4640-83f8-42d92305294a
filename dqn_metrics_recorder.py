#!/usr/bin/env python3
"""
统一的DQN训练指标记录器
用于记录所有DQN变体的训练数据，便于后续制图分析
"""

import os
import json
import numpy as np
import pickle
from datetime import datetime
from collections import deque
import torch

class DQNMetricsRecorder:
    """统一的训练指标记录器"""
    
    def __init__(self, method_name, save_dir="dqn_metrics", base_dir=None):
        self.method_name = method_name
        
        # 设置保存目录
        if base_dir is None:
            self.base_dir = os.path.abspath(".")
        else:
            self.base_dir = base_dir
            
        self.save_dir = os.path.join(self.base_dir, save_dir)
        os.makedirs(self.save_dir, exist_ok=True)
        
        # 初始化指标存储
        self.metrics = {
            'method_name': method_name,
            'start_time': datetime.now().isoformat(),
            'epochs': [],
            'rewards': [],
            'reward_std_window': [],  # 滑动窗口标准差
            'policy_entropy': [],
            'ap50_values': [],
            'map_values': [],
            'convergence_epoch': None,
            'convergence_threshold': -1000,  # 可调整的收敛阈值
            'final_metrics': {},
            'training_completed': False
        }
        
        # 用于计算滑动窗口标准差
        self.reward_window = deque(maxlen=10)  # 最近10个epoch的奖励
        
        print(f"📊 {method_name} 指标记录器初始化完成")
        print(f"💾 数据保存目录: {self.save_dir}")
    
    def record_epoch(self, epoch, reward, actions=None, ap50=None, map_value=None):
        """记录单个epoch的指标"""
        
        # 记录基本指标
        self.metrics['epochs'].append(epoch)
        self.metrics['rewards'].append(float(reward))
        
        # 更新奖励窗口
        self.reward_window.append(reward)
        
        # 计算滑动窗口标准差
        if len(self.reward_window) >= 3:
            reward_std = np.std(list(self.reward_window))
            self.metrics['reward_std_window'].append(float(reward_std))
        else:
            self.metrics['reward_std_window'].append(0.0)
        
        # 计算策略熵
        if actions is not None:
            entropy = self.calculate_policy_entropy(actions)
            self.metrics['policy_entropy'].append(float(entropy))
        else:
            # 如果没有提供actions，使用简化的熵计算（基于epsilon）
            epsilon = max(0.1, 0.9 - (epoch-1) * 0.016)
            entropy = -epsilon * np.log(epsilon + 1e-8) - (1-epsilon) * np.log(1-epsilon + 1e-8)
            self.metrics['policy_entropy'].append(float(entropy))
        
        # 记录AP50和mAP
        if ap50 is not None:
            self.metrics['ap50_values'].append(float(ap50))
        if map_value is not None:
            self.metrics['map_values'].append(float(map_value))
        
        # 检查收敛
        if (self.metrics['convergence_epoch'] is None and 
            reward >= self.metrics['convergence_threshold']):
            self.metrics['convergence_epoch'] = epoch
            print(f"🎯 {self.method_name} 在第 {epoch} 轮达到收敛阈值 {self.metrics['convergence_threshold']}")
        
        # 实时保存数据
        self.save_metrics()
        
        print(f"📈 {self.method_name} Epoch {epoch}: Reward={reward:.2f}, "
              f"Std={self.metrics['reward_std_window'][-1]:.3f}, "
              f"Entropy={self.metrics['policy_entropy'][-1]:.3f}")
    
    def calculate_policy_entropy(self, actions):
        """计算策略熵"""
        if not actions:
            return 0.0
        
        # 计算动作分布
        action_counts = np.bincount(actions, minlength=9)
        action_probs = action_counts / len(actions)
        
        # 计算熵 H = -Σ p(a) * log(p(a))
        entropy = -np.sum(action_probs * np.log(action_probs + 1e-8))
        return entropy
    
    def record_evaluation(self, epoch, ap50, map_value=None, iou_details=None):
        """记录评估结果"""
        
        # 确保epoch存在于记录中
        if epoch not in self.metrics['epochs']:
            print(f"⚠️ Epoch {epoch} 不在记录中，跳过评估记录")
            return
        
        # 找到对应epoch的索引
        try:
            epoch_idx = self.metrics['epochs'].index(epoch)
            
            # 更新或添加AP50值
            while len(self.metrics['ap50_values']) <= epoch_idx:
                self.metrics['ap50_values'].append(0.0)
            self.metrics['ap50_values'][epoch_idx] = float(ap50)
            
            # 更新或添加mAP值
            if map_value is not None:
                while len(self.metrics['map_values']) <= epoch_idx:
                    self.metrics['map_values'].append(0.0)
                self.metrics['map_values'][epoch_idx] = float(map_value)
            
            print(f"📊 {self.method_name} Epoch {epoch} 评估: AP50={ap50:.4f}")
            if map_value is not None:
                print(f"📊 {self.method_name} Epoch {epoch} 评估: mAP={map_value:.4f}")
            
            # 保存更新后的数据
            self.save_metrics()
            
        except ValueError:
            print(f"⚠️ 无法找到 Epoch {epoch} 的记录")
    
    def finalize_training(self):
        """完成训练，计算最终指标"""
        
        if not self.metrics['rewards']:
            print(f"⚠️ {self.method_name} 没有训练数据")
            return
        
        # 计算最终指标
        final_metrics = {}
        
        # 最终奖励
        final_metrics['final_reward'] = float(self.metrics['rewards'][-1])
        
        # 最终奖励稳定性（最后5个epoch的标准差）
        if len(self.metrics['rewards']) >= 5:
            final_rewards = self.metrics['rewards'][-5:]
            final_metrics['final_reward_std'] = float(np.std(final_rewards))
        else:
            final_metrics['final_reward_std'] = float(np.std(self.metrics['rewards']))
        
        # 最终AP50和mAP
        if self.metrics['ap50_values']:
            final_metrics['final_ap50'] = float(self.metrics['ap50_values'][-1])
            final_metrics['max_ap50'] = float(max(self.metrics['ap50_values']))
            final_metrics['avg_ap50'] = float(np.mean(self.metrics['ap50_values']))
        
        if self.metrics['map_values']:
            final_metrics['final_map'] = float(self.metrics['map_values'][-1])
            final_metrics['max_map'] = float(max(self.metrics['map_values']))
            final_metrics['avg_map'] = float(np.mean(self.metrics['map_values']))
        
        # 收敛分析
        final_metrics['convergence_epoch'] = self.metrics['convergence_epoch']
        final_metrics['total_epochs'] = len(self.metrics['epochs'])
        
        # 策略稳定性（最后5个epoch的平均熵）
        if len(self.metrics['policy_entropy']) >= 5:
            final_metrics['final_policy_entropy'] = float(np.mean(self.metrics['policy_entropy'][-5:]))
        elif self.metrics['policy_entropy']:
            final_metrics['final_policy_entropy'] = float(self.metrics['policy_entropy'][-1])
        
        # 训练完成时间
        final_metrics['end_time'] = datetime.now().isoformat()
        
        # 保存最终指标
        self.metrics['final_metrics'] = final_metrics
        self.metrics['training_completed'] = True
        
        # 保存最终数据
        self.save_metrics()
        
        # 打印总结
        print(f"\n{'='*60}")
        print(f"🎉 {self.method_name} 训练完成总结")
        print(f"{'='*60}")
        print(f"📈 最终奖励: {final_metrics['final_reward']:.2f}")
        print(f"📊 奖励稳定性: {final_metrics['final_reward_std']:.3f}")
        if 'final_ap50' in final_metrics:
            print(f"🎯 最终AP50: {final_metrics['final_ap50']:.4f}")
            print(f"🏆 最高AP50: {final_metrics['max_ap50']:.4f}")
        if 'final_map' in final_metrics:
            print(f"🎯 最终mAP: {final_metrics['final_map']:.4f}")
        if final_metrics['convergence_epoch']:
            print(f"⚡ 收敛轮次: {final_metrics['convergence_epoch']}")
        else:
            print(f"⚠️  未达到收敛阈值 {self.metrics['convergence_threshold']}")
        print(f"🧠 最终策略熵: {final_metrics.get('final_policy_entropy', 0):.3f}")
        print(f"⏱️  总训练轮次: {final_metrics['total_epochs']}")
        print(f"{'='*60}")
    
    def save_metrics(self):
        """保存指标数据"""
        
        # 保存为JSON格式（人类可读）
        json_file = os.path.join(self.save_dir, f'{self.method_name}_metrics.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False)
        
        # 保存为pickle格式（用于Python加载）
        pickle_file = os.path.join(self.save_dir, f'{self.method_name}_metrics.pkl')
        with open(pickle_file, 'wb') as f:
            pickle.dump(self.metrics, f)
    
    @staticmethod
    def get_all_metrics(save_dir="dqn_metrics"):
        """获取所有DQN变体的指标数据"""
        all_metrics = {}
        
        if not os.path.exists(save_dir):
            print(f"⚠️ 指标目录不存在: {save_dir}")
            return all_metrics
        
        # 查找所有指标文件
        for file in os.listdir(save_dir):
            if file.endswith('_metrics.pkl'):
                method_name = file.replace('_metrics.pkl', '')
                
                try:
                    with open(os.path.join(save_dir, file), 'rb') as f:
                        metrics = pickle.load(f)
                        all_metrics[method_name] = metrics
                        print(f"✅ 加载 {method_name} 指标数据")
                except Exception as e:
                    print(f"❌ 加载 {method_name} 指标数据失败: {e}")
        
        print(f"📊 总共加载了 {len(all_metrics)} 个DQN变体的指标数据")
        return all_metrics
