def cal_iou(bbx, bbx_gt):  # IOU为0时,两个框不重叠,没有交集。 IOU为1时,两个框完全重叠。bbx_gt为目标框，bbx为探索框
    x1, x2, y1, y2 = bbx
    X1, X2, Y1, Y2 = bbx_gt
    s1 = (y2-y1)*(x2-x1)
    s2 = (Y2-Y1)*(X2-X1)
    jx1 = max(x1, X1)
    jx2 = min(x2, X2)
    jy1 = max(y1, Y1)
    jy2 = min(y2, Y2)
    if jx2 > jx1 and jy2 > jy1:
        s3 = (jx2-jx1)*(jy2-jy1)
    else:
        s3 = 0.0
    return s3/(s1+s2-s3)

class IoUTrendTracker:
    """IoU变化趋势追踪器，用于分析IoU的连续变化模式"""

    def __init__(self, window_size=5):
        """初始化趋势追踪器

        Args:
            window_size: 趋势分析窗口大小，表示保留多少历史IoU记录
        """
        self.iou_history = []  # IoU值历史
        self.iou_diff_history = []  # IoU变化历史（差值）
        self.window_size = window_size  # 分析窗口大小

    def add_iou(self, iou):
        """添加新的IoU值并计算变化

        Args:
            iou: 新的IoU值

        Returns:
            当前IoU与上一步IoU的差值（如果没有上一步则返回0）
        """
        current_diff = 0

        # 计算与上一步的差值
        if self.iou_history:
            current_diff = iou - self.iou_history[-1]
            self.iou_diff_history.append(current_diff)

        # 添加新IoU到历史
        self.iou_history.append(iou)

        # 保持历史记录在窗口大小范围内
        if len(self.iou_history) > self.window_size:
            self.iou_history.pop(0)
        if len(self.iou_diff_history) > self.window_size - 1:
            self.iou_diff_history.pop(0)

        return current_diff

    def detect_trends(self):
        """检测IoU变化趋势

        Returns:
            trend_info: 字典，包含检测到的趋势信息:
                - continuous_improvement: 布尔值，是否连续改进
                - continuous_decline: 布尔值，是否连续下降
                - oscillation: 布尔值，是否存在振荡
                - trend_strength: 浮点数，趋势强度
                - steps: 整数，连续趋势的步数
        """
        trend_info = {
            'continuous_improvement': False,
            'continuous_decline': False,
            'oscillation': False,
            'trend_strength': 0.0,
            'steps': 0
        }

        # 需要至少3个差值才能检测趋势
        if len(self.iou_diff_history) < 3:
            return trend_info

        # 分析最近的变化趋势
        recent_diffs = self.iou_diff_history[-3:]

        # 检测连续改进（全为正值）
        if all(diff > 0 for diff in recent_diffs):
            trend_info['continuous_improvement'] = True
            # 计算连续改进的步数（最多5步）
            steps = 3
            for i in range(3, min(len(self.iou_diff_history), 5)):
                if self.iou_diff_history[-(i+1)] > 0:
                    steps += 1
                else:
                    break
            trend_info['steps'] = steps

        # 检测连续下降（全为负值）
        elif all(diff < 0 for diff in recent_diffs):
            trend_info['continuous_decline'] = True
            # 计算连续下降的步数（最多5步）
            steps = 3
            for i in range(3, min(len(self.iou_diff_history), 5)):
                if self.iou_diff_history[-(i+1)] < 0:
                    steps += 1
                else:
                    break
            trend_info['steps'] = steps

        # 检测振荡（正负交替）
        else:
            sign_changes = 0
            for i in range(1, len(recent_diffs)):
                if recent_diffs[i] * recent_diffs[i-1] < 0:  # 符号变化
                    sign_changes += 1

            if sign_changes >= 1:
                trend_info['oscillation'] = True

        # 计算趋势强度（变化的平均幅度）
        trend_info['trend_strength'] = sum(abs(diff) for diff in recent_diffs) / len(recent_diffs)

        return trend_info

    def get_current_iou(self):
        """获取当前IoU值"""
        if not self.iou_history:
            return 0.0
        return self.iou_history[-1]

    def adjust_window_size(self, current_iou):
        """根据当前IoU调整窗口大小

        Args:
            current_iou: 当前IoU值
        """
        if current_iou > 0.8:
            # 高IoU区域使用小窗口，对变化更敏感
            self.window_size = 3
        elif current_iou < 0.3:
            # 低IoU区域使用大窗口，减少噪声影响
            self.window_size = 6
        else:
            # 中等IoU区域使用标准窗口
            self.window_size = 5

# 全局IoU趋势追踪器
iou_tracker = IoUTrendTracker(window_size=5)

def reward_func(bbx, new_bbx, bbx_gt, action, use_trend=True, training_progress=0.0):
    """增强的奖励函数，支持IoU趋势分析

    Args:
        bbx: 当前边界框 [x1, x2, y1, y2]
        new_bbx: 新边界框 [x1, x2, y1, y2]
        bbx_gt: 目标边界框 [x1, x2, y1, y2]
        action: 执行的动作ID
        use_trend: 是否使用趋势增强（可用于对比实验）
        training_progress: 训练进度(0~1)，用于动态调整趋势权重

    Returns:
        增强后的奖励值
    """
    # 计算IoU
    old_iou = cal_iou(bbx, bbx_gt)
    new_iou = cal_iou(new_bbx, bbx_gt)

    # 更新IoU跟踪器
    global iou_tracker
    iou_tracker.add_iou(new_iou)

    # 计算基础奖励（保持原有逻辑）
    if action == 8:  # 终止动作
        if new_iou >= 0.9:
            base_reward = 3
        else:
            base_reward = -3
    else:  # 非终止动作
        if new_iou > old_iou:
            base_reward = 1
        else:
            base_reward = -1

    # 如果不使用趋势增强，直接返回基础奖励
    if not use_trend:
        return base_reward

    # 趋势奖励计算
    trend_reward = 0
    trend_info = iou_tracker.detect_trends()

    # 趋势权重随训练进度增加
    trend_weight = min(1.0, 0.3 + training_progress * 0.7)

    # 基于IoU区间调整奖励系数
    if new_iou < 0.4:  # 低IoU区域
        improvement_coef = 1.5
        decline_coef = 0.8
        oscillation_coef = 0.5
    elif new_iou < 0.7:  # 中IoU区域
        improvement_coef = 1.2
        decline_coef = 1.0
        oscillation_coef = 0.7
    else:  # 高IoU区域
        improvement_coef = 0.8
        decline_coef = 1.5
        oscillation_coef = 1.0

    # 连续改进奖励
    if trend_info['continuous_improvement']:
        # 奖励随连续步数增加
        steps_factor = min(2.0, 1.0 + (trend_info['steps'] - 3) * 0.25)
        trend_reward += improvement_coef * trend_info['trend_strength'] * steps_factor

    # 连续下降惩罚
    elif trend_info['continuous_decline']:
        # 惩罚随连续步数增加
        steps_factor = min(2.0, 1.0 + (trend_info['steps'] - 3) * 0.25)
        trend_reward -= decline_coef * trend_info['trend_strength'] * steps_factor

    # 振荡惩罚
    elif trend_info['oscillation']:
        trend_reward -= oscillation_coef * 0.5

    # 如果是终止动作，趋势奖励需要特殊处理
    if action == 8:
        if trend_info['continuous_improvement'] and base_reward > 0:
            # 在正向趋势下终止且IoU高，增强奖励
            trend_reward *= 1.5
        elif trend_info['continuous_decline'] and base_reward < 0:
            # 在负向趋势下终止且IoU低，增强惩罚
            trend_reward *= 1.5

    # 应用趋势权重
    trend_reward *= trend_weight

    # 组合最终奖励
    final_reward = base_reward + trend_reward

    return final_reward